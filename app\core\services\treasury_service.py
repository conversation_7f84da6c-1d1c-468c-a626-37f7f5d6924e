"""
Service pour la gestion de la trésorerie.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.core.models.treasury import (
    CashRegister, CashRegisterPydantic,
    CashTransaction, CashTransactionPydantic,
    Expense, ExpensePydantic,
    CashRegisterType, TransactionCategory, PaymentMethod
)
from app.core.services.base_service import BaseService
from app.core.services.notification_service import NotificationService
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel

class TreasuryService(BaseService[CashRegister, CashRegisterPydantic, CashRegisterPydantic]):
    """Service pour la gestion de la trésorerie"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
            print("TreasuryService: Nouvelle session de base de données créée")
        else:
            print("TreasuryService: Session de base de données existante utilisée")
        super().__init__(db, CashRegister)
        self.notification_service = NotificationService(db)

    async def create_cash_register(self, data: Dict[str, Any]) -> CashRegister:
        """
        Crée une nouvelle caisse

        Args:
            data: Données de la caisse

        Returns:
            La caisse créée
        """
        # Vérifier si une caisse avec le même nom existe déjà
        existing = self.db.query(self.model).filter(self.model.name == data["name"]).first()
        if existing:
            raise ValueError(f"Une caisse avec le nom '{data['name']}' existe déjà")

        # Créer la caisse
        cash_register = CashRegister(
            name=data["name"],
            type=data["type"],
            initial_balance=data.get("initial_balance", 0.0),
            current_balance=data.get("initial_balance", 0.0),  # Au départ, le solde courant est égal au solde initial
            notes=data.get("notes")
        )
        self.db.add(cash_register)
        self.db.commit()
        self.db.refresh(cash_register)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Nouvelle caisse créée",
            "message": f"La caisse '{cash_register.name}' a été créée avec un solde initial de {cash_register.initial_balance} DA.",
            "data": {"cash_register_id": cash_register.id},
            "action_url": f"/treasury/cash-registers/{cash_register.id}",
            "icon": "money",
            "channels": [NotificationChannel.UI]
        })

        return cash_register

    async def add_transaction(self, data: Dict[str, Any]) -> CashTransaction:
        """
        Ajoute une transaction à une caisse

        Args:
            data: Données de la transaction

        Returns:
            La transaction créée
        """
        # Vérifier la caisse
        cash_register_id = data["cash_register_id"]
        cash_register = self.db.query(CashRegister).get(cash_register_id)
        if not cash_register:
            raise ValueError(f"Caisse avec ID {cash_register_id} non trouvée")

        # Créer la transaction
        transaction = CashTransaction(
            cash_register_id=cash_register_id,
            amount=data["amount"],
            transaction_date=data.get("transaction_date", datetime.utcnow()),
            category=data["category"],
            payment_method=data["payment_method"],
            reference_number=data.get("reference_number"),
            description=data.get("description"),
            sale_id=data.get("sale_id"),
            repair_id=data.get("repair_id"),
            purchase_id=data.get("purchase_id"),
            supplier_payment_id=data.get("supplier_payment_id"),
            customer_transaction_id=data.get("customer_transaction_id"),
            expense_id=data.get("expense_id"),
            user_id=data["user_id"]
        )
        self.db.add(transaction)

        # Mettre à jour le solde de la caisse
        cash_register.current_balance += transaction.amount

        self.db.commit()
        self.db.refresh(transaction)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": data["user_id"],
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Nouvelle transaction de caisse",
            "message": f"Une transaction de {transaction.amount} DA a été enregistrée dans la caisse '{cash_register.name}'.",
            "data": {"transaction_id": transaction.id, "cash_register_id": cash_register_id},
            "action_url": f"/treasury/transactions/{transaction.id}",
            "icon": "money",
            "channels": [NotificationChannel.UI]
        })

        # Notifier la mise à jour de la trésorerie
        try:
            from app.utils.treasury_updater import notify_transaction_added
            notify_transaction_added(
                cash_register_id=cash_register_id,
                transaction_amount=transaction.amount,
                transaction_type=transaction.category.value if hasattr(transaction.category, 'value') else str(transaction.category)
            )
        except Exception as e:
            print(f"Erreur lors de la notification de mise à jour de la trésorerie: {e}")

        return transaction

    async def add_expense(self, data: Dict[str, Any]) -> Expense:
        """
        Ajoute une dépense

        Args:
            data: Données de la dépense

        Returns:
            La dépense créée
        """
        # Vérifier la caisse
        cash_register_id = data["cash_register_id"]
        cash_register = self.db.query(CashRegister).get(cash_register_id)
        if not cash_register:
            raise ValueError(f"Caisse avec ID {cash_register_id} non trouvée")

        # Vérifier que le solde est suffisant
        amount = data["amount"]
        if cash_register.current_balance < amount:
            raise ValueError(f"Solde insuffisant dans la caisse '{cash_register.name}' pour cette dépense")

        # Créer la dépense
        expense = Expense(
            amount=amount,
            expense_date=data.get("expense_date", datetime.utcnow()),
            category=data["category"],
            payment_method=data["payment_method"],
            reference_number=data.get("reference_number"),
            description=data.get("description"),
            receipt_image=data.get("receipt_image"),
            cash_register_id=cash_register_id,
            user_id=data["user_id"]
        )
        self.db.add(expense)
        self.db.flush()  # Pour obtenir l'ID de la dépense

        # Créer une transaction de caisse négative pour la dépense
        transaction = CashTransaction(
            cash_register_id=cash_register_id,
            amount=-amount,  # Montant négatif pour une dépense
            transaction_date=data.get("expense_date", datetime.utcnow()),
            category=TransactionCategory.EXPENSE,
            payment_method=data["payment_method"],
            reference_number=data.get("reference_number"),
            description=data.get("description"),
            expense_id=expense.id,
            user_id=data["user_id"]
        )
        self.db.add(transaction)

        # Mettre à jour le solde de la caisse
        cash_register.current_balance -= amount

        self.db.commit()
        self.db.refresh(expense)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": data["user_id"],
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Nouvelle dépense enregistrée",
            "message": f"Une dépense de {amount} DA a été enregistrée dans la caisse '{cash_register.name}'.",
            "data": {"expense_id": expense.id, "cash_register_id": cash_register_id},
            "action_url": f"/treasury/expenses/{expense.id}",
            "icon": "money",
            "channels": [NotificationChannel.UI]
        })

        return expense

    async def transfer_between_registers(self, from_id: int, to_id: int, amount: float, user_id: int, description: str = None) -> Dict[str, Any]:
        """
        Transfère des fonds entre deux caisses

        Args:
            from_id: ID de la caisse source
            to_id: ID de la caisse destination
            amount: Montant à transférer
            user_id: ID de l'utilisateur effectuant le transfert
            description: Description du transfert

        Returns:
            Dictionnaire contenant les informations sur le transfert
        """
        # Vérifier les caisses
        from_register = self.db.query(CashRegister).get(from_id)
        if not from_register:
            raise ValueError(f"Caisse source avec ID {from_id} non trouvée")

        to_register = self.db.query(CashRegister).get(to_id)
        if not to_register:
            raise ValueError(f"Caisse destination avec ID {to_id} non trouvée")

        # Vérifier que le solde est suffisant
        if from_register.current_balance < amount:
            raise ValueError(f"Solde insuffisant dans la caisse '{from_register.name}' pour ce transfert")

        # Créer une transaction de sortie pour la caisse source
        out_transaction = CashTransaction(
            cash_register_id=from_id,
            amount=-amount,  # Montant négatif pour une sortie
            transaction_date=datetime.utcnow(),
            category=TransactionCategory.TRANSFER,
            payment_method=PaymentMethod.cash,
            reference_number=f"TRANSFER-{datetime.utcnow().strftime('%Y%m%d%H%M%S')}",
            description=description or f"Transfert vers la caisse '{to_register.name}'",
            user_id=user_id
        )
        self.db.add(out_transaction)

        # Créer une transaction d'entrée pour la caisse destination
        in_transaction = CashTransaction(
            cash_register_id=to_id,
            amount=amount,  # Montant positif pour une entrée
            transaction_date=datetime.utcnow(),
            category=TransactionCategory.TRANSFER,
            payment_method=PaymentMethod.cash,
            reference_number=f"TRANSFER-{datetime.utcnow().strftime('%Y%m%d%H%M%S')}",
            description=description or f"Transfert depuis la caisse '{from_register.name}'",
            user_id=user_id
        )
        self.db.add(in_transaction)

        # Mettre à jour les soldes des caisses
        from_register.current_balance -= amount
        to_register.current_balance += amount

        self.db.commit()
        self.db.refresh(out_transaction)
        self.db.refresh(in_transaction)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": user_id,
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Transfert entre caisses",
            "message": f"Un transfert de {amount} DA a été effectué de la caisse '{from_register.name}' vers la caisse '{to_register.name}'.",
            "data": {"from_id": from_id, "to_id": to_id, "amount": amount},
            "action_url": f"/treasury/transactions/{out_transaction.id}",
            "icon": "money",
            "channels": [NotificationChannel.UI]
        })

        return {
            "from_register": from_register,
            "to_register": to_register,
            "amount": amount,
            "out_transaction": out_transaction,
            "in_transaction": in_transaction
        }

    async def reconcile_cash_register(self, register_id: int, counted_amount: float, user_id: int, notes: str = None) -> Dict[str, Any]:
        """
        Réconcilie une caisse (ajuste le solde en fonction du comptage physique)

        Args:
            register_id: ID de la caisse
            counted_amount: Montant compté physiquement
            user_id: ID de l'utilisateur effectuant la réconciliation
            notes: Notes sur la réconciliation

        Returns:
            Dictionnaire contenant les informations sur la réconciliation
        """
        # Vérifier la caisse
        cash_register = self.db.query(CashRegister).get(register_id)
        if not cash_register:
            raise ValueError(f"Caisse avec ID {register_id} non trouvée")

        # Calculer la différence
        difference = counted_amount - cash_register.current_balance
        previous_balance = cash_register.current_balance

        # Créer une transaction d'ajustement si nécessaire
        if difference != 0:
            adjustment = CashTransaction(
                cash_register_id=register_id,
                amount=difference,
                transaction_date=datetime.now(),
                category=TransactionCategory.ADJUSTMENT,
                payment_method=PaymentMethod.cash,
                reference_number=f"RECONCILE-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                description=notes or f"Ajustement lors de la réconciliation de caisse",
                user_id=user_id
            )
            self.db.add(adjustment)

            # Mettre à jour le solde de la caisse
            cash_register.current_balance = counted_amount

        # Mettre à jour la date de dernière réconciliation
        cash_register.last_reconciliation = datetime.now()

        self.db.commit()
        self.db.refresh(cash_register)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": user_id,
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Réconciliation de caisse",
            "message": f"La caisse '{cash_register.name}' a été réconciliée avec un ajustement de {difference} DA.",
            "data": {"cash_register_id": register_id, "difference": difference},
            "action_url": f"/treasury/cash-registers/{register_id}",
            "icon": "money",
            "channels": [NotificationChannel.UI]
        })

        return {
            "cash_register": cash_register,
            "counted_amount": counted_amount,
            "previous_balance": previous_balance,
            "difference": difference,
            "reconciliation_date": cash_register.last_reconciliation
        }

    async def get_cash_register_summary(self, register_id: int) -> Dict[str, Any]:
        """
        Récupère un résumé des transactions d'une caisse

        Args:
            register_id: ID de la caisse

        Returns:
            Dictionnaire contenant le résumé
        """
        # Vérifier la caisse
        cash_register = self.db.query(CashRegister).get(register_id)
        if not cash_register:
            raise ValueError(f"Caisse avec ID {register_id} non trouvée")

        # Récupérer les transactions de la journée
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_transactions = self.db.query(CashTransaction).filter(
            CashTransaction.cash_register_id == register_id,
            CashTransaction.transaction_date >= today
        ).all()

        # Calculer les totaux par catégorie
        category_totals = {}
        for transaction in today_transactions:
            category = transaction.category.value
            if category not in category_totals:
                category_totals[category] = 0
            category_totals[category] += transaction.amount

        # Calculer les totaux par méthode de paiement
        payment_method_totals = {}
        for transaction in today_transactions:
            method = transaction.payment_method.value
            if method not in payment_method_totals:
                payment_method_totals[method] = 0
            payment_method_totals[method] += transaction.amount

        # Calculer le total des entrées et sorties
        total_in = sum(t.amount for t in today_transactions if t.amount > 0)
        total_out = sum(t.amount for t in today_transactions if t.amount < 0)

        return {
            "cash_register": cash_register,
            "today_transactions": today_transactions,
            "category_totals": category_totals,
            "payment_method_totals": payment_method_totals,
            "total_in": total_in,
            "total_out": total_out,
            "net_change": total_in + total_out,
            "transaction_count": len(today_transactions)
        }

    async def get_treasury_dashboard_data(self) -> Dict[str, Any]:
        """
        Récupère les données pour le tableau de bord de trésorerie

        Returns:
            Dictionnaire contenant les données du tableau de bord
        """
        # Récupérer toutes les caisses actives
        cash_registers = self.db.query(CashRegister).filter(CashRegister.is_active == True).all()

        # Calculer le solde total
        total_balance = sum(register.current_balance for register in cash_registers)

        # Récupérer les transactions récentes (30 dernières)
        recent_transactions = self.db.query(CashTransaction).order_by(desc(CashTransaction.transaction_date)).limit(30).all()

        # Récupérer les dépenses récentes (30 dernières)
        recent_expenses = self.db.query(Expense).order_by(desc(Expense.expense_date)).limit(30).all()

        # Calculer les totaux par type de caisse
        register_type_totals = {}
        for register in cash_registers:
            register_type = register.type.value
            if register_type not in register_type_totals:
                register_type_totals[register_type] = 0
            register_type_totals[register_type] += register.current_balance

        # Calculer les totaux des transactions du jour
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_transactions = self.db.query(CashTransaction).filter(
            CashTransaction.transaction_date >= today
        ).all()

        today_in = sum(t.amount for t in today_transactions if t.amount > 0)
        today_out = sum(t.amount for t in today_transactions if t.amount < 0)

        return {
            "cash_registers": cash_registers,
            "total_balance": total_balance,
            "recent_transactions": recent_transactions,
            "recent_expenses": recent_expenses,
            "register_type_totals": register_type_totals,
            "today_in": today_in,
            "today_out": today_out,
            "today_net": today_in + today_out
        }
