from PyQt6.QtWidgets import (Q<PERSON>ain<PERSON><PERSON><PERSON>, QStackedWidget, QToolBar, QStatusBar, QMenu,
                           QWidget, QVBoxLayout, QSizePolicy, QLabel, QPushButton,
                           QHBoxLayout, QMessageBox, QToolButton)
from PyQt6.QtCore import Qt, QSize, pyqtSlot, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QFont

# Importer les icônes Unicode
from app.ui.components.unicode_icons import UnicodeIcons
from app.controllers.auth_controller import AuthController
from app.ui.views.dashboard.dashboard_view import DashboardView
from app.ui.views.inventory.inventory_view import InventoryView
from app.ui.views.inventory.category_view import CategoryView
from app.ui.views.repair.repair_view import RepairView
from app.ui.views.customer.customer_view import CustomerView
from app.ui.views.customer.customer_finance_view import CustomerFinanceView
from app.ui.views.supplier.supplier_view import SupplierView
from app.ui.views.equipment.equipment_view import EquipmentView
from app.ui.views.reporting.reporting_view import ReportingView
from app.ui.views.purchasing.purchasing_view import PurchasingView
from app.ui.views.user.user_view import UserView
from app.ui.views.sale.sale_view import SaleView
from app.ui.views.treasury.treasury_view import TreasuryView
from app.ui.views.settings.settings_view import SettingsView
from app.ui.components.notification_center import NotificationButton, NotificationCenter
from app.ui.theme.theme_manager import ThemeManager
from app.ui.window_cleanup import cleanup_toolbar
from app.ui.resources import resources_rc as _

class MainWindow(QMainWindow):
    # Signaux pour la déconnexion et le changement d'utilisateur
    logoutRequested = pyqtSignal()
    switchUserRequested = pyqtSignal()

    def __init__(self, user_info=None):
        super().__init__()
        self.setWindowTitle("Nadjib-GSM - Gestionnaire de stocks et de maintenance")
        self.setMinimumSize(1200, 800)

        # Définir l'icône de la fenêtre
        self.setWindowIcon(QIcon("app/ui/resources/images/app.ico"))

        # Stocker les informations de l'utilisateur
        self.user_info = user_info

        # Créer le contrôleur d'authentification
        self.auth_controller = AuthController()
        if user_info:
            self.auth_controller.current_user = user_info

        # Configuration de l'interface
        self._setup_ui()
        self._create_toolbar()
        self._create_statusbar()
        self._create_user_menu()
        self._setup_navigation()

        # Chargement du thème par défaut
        self._load_theme()

    def _setup_ui(self):
        """Configure les éléments principaux de l'interface."""
        self.central_widget = QStackedWidget()
        self.setCentralWidget(self.central_widget)

        # Ajout des vues principales
        self.dashboard = DashboardView()
        self.inventory = InventoryView()
        self.category = CategoryView()
        self.repair = RepairView()
        self.customer = CustomerView()
        self.customer_finance = CustomerFinanceView()
        self.supplier = SupplierView()
        self.equipment = EquipmentView()
        self.reporting = ReportingView()
        self.purchasing = PurchasingView()
        self.sale = SaleView()
        self.user = UserView()
        self.treasury = TreasuryView()
        self.settings = SettingsView()

        self.central_widget.addWidget(self.dashboard)
        self.central_widget.addWidget(self.inventory)
        self.central_widget.addWidget(self.category)
        self.central_widget.addWidget(self.repair)
        self.central_widget.addWidget(self.customer)
        self.central_widget.addWidget(self.customer_finance)
        self.central_widget.addWidget(self.supplier)
        self.central_widget.addWidget(self.equipment)
        self.central_widget.addWidget(self.reporting)
        self.central_widget.addWidget(self.purchasing)
        self.central_widget.addWidget(self.sale)
        self.central_widget.addWidget(self.user)
        self.central_widget.addWidget(self.treasury)
        self.central_widget.addWidget(self.settings)

    def _create_toolbar(self):
        """Crée la barre d'outils principale."""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        toolbar.setObjectName("mainToolBar")
        self.addToolBar(Qt.ToolBarArea.LeftToolBarArea, toolbar)

        # Mise à jour des chemins d'icônes
        # Bouton du tableau de bord avec gestionnaire d'événements spécifique
        self.dashboard_action = toolbar.addAction(QIcon("app/ui/resources/icons/dashboard.svg"), "Tableau de bord")
        self.dashboard_action.triggered.connect(self.show_dashboard_view)

        # Bouton de l'inventaire
        self.inventory_action = toolbar.addAction(QIcon("app/ui/resources/icons/inventory.svg"), "Inventaire")
        self.inventory_action.triggered.connect(self.show_inventory_view)

        # Bouton des réparations
        self.repair_action = toolbar.addAction(QIcon("app/ui/resources/icons/repair.svg"), "Réparations")
        self.repair_action.triggered.connect(self.show_repair_view)

        # Bouton des clients
        self.customer_action = toolbar.addAction(QIcon("app/ui/resources/icons/customer.svg"), "Clients")
        self.customer_action.triggered.connect(self.show_customer_view)

        # Bouton des fournisseurs
        self.supplier_action = toolbar.addAction(QIcon("app/ui/resources/icons/supplier.svg"), "Fournisseurs")
        self.supplier_action.triggered.connect(self.show_supplier_view)

        # Bouton des achats
        self.purchase_action = toolbar.addAction(QIcon("app/ui/resources/icons/purchase.svg"), "Achats")
        self.purchase_action.triggered.connect(self.show_purchase_view)

        # Créer une barre d'outils pour les actions globales (en haut à droite)
        global_toolbar = QToolBar()
        global_toolbar.setMovable(False)
        global_toolbar.setObjectName("globalToolBar")
        self.addToolBar(Qt.ToolBarArea.RightToolBarArea, global_toolbar)

        # Bouton de basculement de thème
        self.theme_action = QAction(self)
        self.theme_action.setToolTip("Basculer entre le thème clair et sombre")
        self.theme_action.triggered.connect(self._toggle_theme)

        # Définir l'icône en fonction du thème actuel
        self._update_theme_icon()

        global_toolbar.addAction(self.theme_action)

        # Ajouter les actions supplémentaires de manière plus structurée
        # Équipements
        equipment_action = toolbar.addAction(QIcon("app/ui/resources/icons/equipment.svg"), "Équipements")
        equipment_action.triggered.connect(lambda: self.central_widget.setCurrentWidget(self.equipment))

        # Finances clients
        self.customer_finance_action = toolbar.addAction(QIcon("app/ui/resources/icons/finance.svg"), "Finances clients")
        self.customer_finance_action.triggered.connect(self.show_customer_finance_view)

        # Ventes
        sales_action = toolbar.addAction(QIcon("app/ui/resources/icons/sale.svg"), "Ventes")
        sales_action.triggered.connect(lambda: self.central_widget.setCurrentWidget(self.sale))

        # Trésorerie
        treasury_action = toolbar.addAction(QIcon("app/ui/resources/icons/money.svg"), "Trésorerie")
        treasury_action.triggered.connect(lambda: self.central_widget.setCurrentWidget(self.treasury))

        # Rapports & Statistiques
        reporting_action = toolbar.addAction(QIcon("app/ui/resources/icons/chart.svg"), "Rapports & Statistiques")
        reporting_action.triggered.connect(lambda: self.central_widget.setCurrentWidget(self.reporting))

        # Utilisateurs
        user_action = toolbar.addAction(QIcon("app/ui/resources/icons/user.svg"), "Utilisateurs")
        user_action.triggered.connect(lambda: self.central_widget.setCurrentWidget(self.user))

        # Ajouter un espace extensible
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        toolbar.addWidget(spacer)

        # Bouton de paramètres avec icône SVG
        settings_button = QToolButton()
        settings_button.setObjectName("settingsButton")
        settings_button.setIcon(QIcon("app/ui/resources/icons/settings_new.svg"))
        settings_button.setIconSize(QSize(24, 24))
        settings_button.setToolTip("Paramètres")
        settings_button.setCursor(Qt.CursorShape.PointingHandCursor)
        settings_button.setFixedSize(32, 32)
        settings_button.setStyleSheet("""
            QToolButton {
                background-color: transparent;
                border: none;
                color: #2196F3;
                padding: 4px;
                margin: 0px;
            }
            QToolButton:hover {
                background-color: rgba(33, 150, 243, 0.1);
            }
        """)
        settings_button.clicked.connect(lambda: self.central_widget.setCurrentWidget(self.settings))

        # Ajouter le bouton à la barre d'outils
        toolbar.addWidget(settings_button)

        # Créer une action pour le bouton (pour la cohérence avec le reste du code)
        self.settings_action = QAction(QIcon("app/ui/resources/icons/settings_new.svg"), "Paramètres", self)
        self.settings_action.triggered.connect(lambda: self.central_widget.setCurrentWidget(self.settings))

        # Bouton de notification
        self.notification_button = NotificationButton()
        self.notification_button.clicked.connect(self._toggle_notification_center)
        toolbar.addWidget(self.notification_button)

        # Centre de notifications (initialement caché)
        self.notification_center = NotificationCenter()
        self.notification_center.hide()
        self.notification_center.notificationClicked.connect(self._handle_notification_click)
        self.notification_center.unreadCountChanged.connect(self._update_notification_badge)

    def _create_statusbar(self):
        """Configure la barre de statut."""
        status = QStatusBar()
        self.setStatusBar(status)

        # Ajouter le label pour l'utilisateur actuel
        if self.user_info:
            user_label = QLabel(f"Utilisateur: {self.user_info.get('full_name', 'Inconnu')}")
            user_label.setStyleSheet("color: #2980b9; font-weight: bold; padding-right: 10px;")
            status.addPermanentWidget(user_label)

        status.showMessage("Prêt")

    def _create_user_menu(self):
        """Crée le menu utilisateur avec les options de déconnexion, changement d'utilisateur et quitter."""
        # Créer la barre de menu
        menubar = self.menuBar()

        # Créer le menu utilisateur
        user_menu = menubar.addMenu("&Utilisateur")

        # Créer le menu d'affichage
        view_menu = menubar.addMenu("&Affichage")

        # Sous-menu Thème
        theme_menu = view_menu.addMenu("&Thème")

        # Action pour thème clair
        light_theme_action = QAction("Thème &Clair", self)
        light_theme_action.setStatusTip("Basculer vers le thème clair")
        light_theme_action.triggered.connect(lambda: self._switch_theme("light"))
        theme_menu.addAction(light_theme_action)

        # Action pour thème sombre
        dark_theme_action = QAction("Thème &Sombre", self)
        dark_theme_action.setStatusTip("Basculer vers le thème sombre")
        dark_theme_action.triggered.connect(lambda: self._switch_theme("dark"))
        theme_menu.addAction(dark_theme_action)

        theme_menu.addSeparator()

        # Action pour basculer automatiquement
        toggle_theme_action = QAction("&Basculer le Thème", self)
        toggle_theme_action.setShortcut("Ctrl+T")
        toggle_theme_action.setStatusTip("Basculer entre thème clair et sombre")
        toggle_theme_action.triggered.connect(self._toggle_theme)
        theme_menu.addAction(toggle_theme_action)

        # Option pour afficher les informations de l'utilisateur
        if self.user_info:
            user_info_action = QAction(f"Connecté en tant que: {self.user_info.get('full_name', 'Inconnu')}", self)
            user_info_action.setEnabled(False)
            user_menu.addAction(user_info_action)

            # Afficher les rôles de l'utilisateur
            roles = self.user_info.get('permissions', [])
            if roles:
                roles_str = ", ".join(roles)
                roles_action = QAction(f"Rôles: {roles_str}", self)
                roles_action.setEnabled(False)
                user_menu.addAction(roles_action)

            user_menu.addSeparator()

        # Option pour se déconnecter
        logout_action = QAction("&Déconnecter", self)
        logout_action.setStatusTip("Se déconnecter de l'application")
        logout_action.triggered.connect(self._handle_logout)
        user_menu.addAction(logout_action)

        # Option pour changer d'utilisateur
        switch_user_action = QAction("Changer d'&utilisateur", self)
        switch_user_action.setStatusTip("Se connecter avec un autre compte")
        switch_user_action.triggered.connect(self._handle_switch_user)
        user_menu.addAction(switch_user_action)

        user_menu.addSeparator()

        # Option pour quitter
        exit_action = QAction("&Quitter", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("Quitter l'application")
        exit_action.triggered.connect(self.close)
        user_menu.addAction(exit_action)

    def _handle_logout(self):
        """Gère la déconnexion de l'utilisateur."""
        reply = QMessageBox.question(
            self,
            "Confirmation de déconnexion",
            "Voulez-vous vraiment vous déconnecter ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Émettre le signal de déconnexion
            self.logoutRequested.emit()

    def _handle_switch_user(self):
        """Gère le changement d'utilisateur."""
        reply = QMessageBox.question(
            self,
            "Confirmation de changement d'utilisateur",
            "Voulez-vous vraiment changer d'utilisateur ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Émettre le signal de changement d'utilisateur
            self.switchUserRequested.emit()

    def update_user_interface(self):
        """Met à jour l'interface utilisateur en fonction des informations de l'utilisateur."""
        # Mettre à jour le menu utilisateur
        self._create_user_menu()

        # Mettre à jour la barre d'état
        status = self.statusBar()

        # Supprimer l'ancien label utilisateur s'il existe
        for widget in status.findChildren(QLabel):
            if widget.text().startswith("Utilisateur:"):
                status.removeWidget(widget)
                widget.deleteLater()

        # Ajouter le nouveau label utilisateur
        if self.user_info:
            user_label = QLabel(f"Utilisateur: {self.user_info.get('full_name', 'Inconnu')}")
            user_label.setStyleSheet("color: #2980b9; font-weight: bold; padding-right: 10px;")
            status.addPermanentWidget(user_label)

        # Appliquer les restrictions basées sur les rôles
        self._apply_role_restrictions()

    def _setup_navigation(self):
        """Configure la navigation entre les vues et applique les restrictions basées sur les rôles."""
        # Vérifier les permissions de l'utilisateur
        self._apply_role_restrictions()

    def _toggle_notification_center(self):
        """Affiche ou masque le centre de notifications"""
        if self.notification_center.isVisible():
            self.notification_center.hide()
        else:
            # Positionner le centre de notifications
            button_pos = self.notification_button.mapToGlobal(self.notification_button.rect().bottomLeft())
            self.notification_center.setGeometry(
                button_pos.x() - 300 + self.notification_button.width(),
                button_pos.y() + 10,
                350,
                500
            )
            self.notification_center.show()

            # Rafraîchir les notifications (désactivé pour éviter les doublons)
            # self.notification_center.refresh_notifications()

    def _handle_notification_click(self, notification_id, action_url):
        """Gère le clic sur une notification"""
        # Masquer le centre de notifications
        self.notification_center.hide()

        # Naviguer vers l'URL d'action si elle existe
        if action_url:
            # TODO: Implémenter la navigation vers l'URL d'action
            print(f"Navigation vers {action_url}")

    def _update_notification_badge(self, count):
        """Met à jour le badge du bouton de notification"""
        self.notification_button.set_unread_count(count)

    def show_customer_view(self):
        """Affiche la vue de gestion des clients"""
        print("Affichage de la vue de gestion des clients")
        self.central_widget.setCurrentWidget(self.customer)

    def show_customer_finance_view(self):
        """Affiche la vue de gestion financière des clients"""
        print("Affichage de la vue de gestion financière des clients")
        self.central_widget.setCurrentWidget(self.customer_finance)

    def show_dashboard_view(self):
        """Affiche la vue du tableau de bord et rafraîchit les données"""
        print("Affichage du tableau de bord")
        self.central_widget.setCurrentWidget(self.dashboard)

        # Rafraîchir les données du tableau de bord
        if hasattr(self.dashboard, 'refresh'):
            print("Rafraîchissement des données du tableau de bord")
            self.dashboard.refresh()

    def _load_theme(self):
        """Charge le thème de l'application"""
        self.theme_manager = ThemeManager()

        # Charger le thème sauvegardé ou par défaut
        self.theme_manager.apply_theme_to_app()

    def _switch_theme(self, theme_name: str):
        """Bascule vers un thème spécifique"""
        try:
            self.theme_manager.switch_theme(theme_name)
            self.theme_manager.apply_theme_to_app()
            self.statusBar().showMessage(f"Thème basculé vers: {theme_name.title()}", 3000)
        except Exception as e:
            QMessageBox.warning(self, "Erreur de Thème", f"Impossible de charger le thème {theme_name}: {str(e)}")

    def _toggle_theme(self):
        """Bascule entre thème clair et sombre"""
        try:
            self.theme_manager.toggle_theme()
            self.theme_manager.apply_theme_to_app()
            current_theme = self.theme_manager.current_theme
            self.statusBar().showMessage(f"Thème basculé vers: {current_theme.title()}", 3000)
        except Exception as e:
            QMessageBox.warning(self, "Erreur de Thème", f"Impossible de basculer le thème: {str(e)}")



    def _apply_role_restrictions(self):
        """Applique les restrictions basées sur les rôles de l'utilisateur."""
        if not self.user_info:
            return

        # Récupérer les rôles de l'utilisateur
        roles = self.user_info.get('permissions', [])

        # Si l'utilisateur est administrateur, il a accès à tout
        if 'admin' in roles:
            return

        # Définir les restrictions par rôle
        role_restrictions = {
            'manager': {
                'allowed': ['dashboard', 'inventory', 'repair', 'customer', 'supplier', 'reporting'],
                'denied': ['user', 'settings']
            },
            'technician': {
                'allowed': ['dashboard', 'repair', 'inventory'],
                'denied': ['user', 'customer', 'supplier', 'reporting', 'settings', 'purchasing', 'treasury']
            },
            'sales': {
                'allowed': ['dashboard', 'customer', 'sale', 'inventory'],
                'denied': ['user', 'repair', 'supplier', 'settings', 'treasury']
            },
            'inventory': {
                'allowed': ['dashboard', 'inventory', 'supplier', 'purchasing'],
                'denied': ['user', 'repair', 'customer', 'settings', 'treasury', 'sale']
            }
        }

        # Appliquer les restrictions
        for role in roles:
            if role in role_restrictions:
                # Désactiver les vues non autorisées
                for view_name in role_restrictions[role]['denied']:
                    if hasattr(self, view_name):
                        view = getattr(self, view_name)
                        if view in self.central_widget.findChildren(QWidget):
                            # Trouver l'action correspondante dans la barre d'outils
                            for action in self.findChildren(QAction):
                                if action.text().lower() == view_name.lower() or view_name.lower() in action.text().lower():
                                    action.setEnabled(False)
                                    action.setToolTip(f"Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité")

        # Afficher un message dans la barre d'état
        self.statusBar().showMessage(f"Connecté avec des permissions limitées. Rôles: {', '.join(roles)}")





    def _update_toolbar_icons(self):
        """Met à jour les icônes de la barre d'outils avec les versions standard"""
        if hasattr(self, 'dashboard_action'):
            self.dashboard_action.setIcon(QIcon("app/ui/resources/icons/dashboard.svg"))

        if hasattr(self, 'inventory_action'):
            self.inventory_action.setIcon(QIcon("app/ui/resources/icons/inventory.svg"))

        if hasattr(self, 'repair_action'):
            self.repair_action.setIcon(QIcon("app/ui/resources/icons/repair.svg"))

        if hasattr(self, 'customer_action'):
            self.customer_action.setIcon(QIcon("app/ui/resources/icons/customer.svg"))

        if hasattr(self, 'supplier_action'):
            self.supplier_action.setIcon(QIcon("app/ui/resources/icons/supplier.svg"))

        if hasattr(self, 'purchase_action'):
            self.purchase_action.setIcon(QIcon("app/ui/resources/icons/purchase.svg"))

        if hasattr(self, 'customer_finance_action'):
            self.customer_finance_action.setIcon(QIcon("app/ui/resources/icons/finance.svg"))

        # Mettre à jour les actions de la barre d'outils
        for action in self.findChildren(QAction):
            if action.text() == "Équipements":
                action.setIcon(QIcon("app/ui/resources/icons/equipment.svg"))
            elif action.text() == "Ventes":
                action.setIcon(QIcon("app/ui/resources/icons/sale.svg"))
            elif action.text() == "Trésorerie":
                action.setIcon(QIcon("app/ui/resources/icons/money.svg"))
            elif action.text() == "Rapports & Statistiques":
                action.setIcon(QIcon("app/ui/resources/icons/chart.svg"))
            elif action.text() == "Utilisateurs":
                action.setIcon(QIcon("app/ui/resources/icons/user.svg"))
            elif action.text() == "Paramètres" or action.text() == "⚙️ Paramètres":
                # Ne rien faire, nous utilisons maintenant un QToolButton avec du texte
                pass

    def show_inventory_view(self):
        """Affiche la vue de gestion de l'inventaire"""
        print("Affichage de la vue de gestion de l'inventaire")
        self.central_widget.setCurrentWidget(self.inventory)

    def show_repair_view(self):
        """Affiche la vue de gestion des réparations"""
        print("Affichage de la vue de gestion des réparations")
        self.central_widget.setCurrentWidget(self.repair)

    def show_supplier_view(self):
        """Affiche la vue de gestion des fournisseurs"""
        print("Affichage de la vue de gestion des fournisseurs")
        self.central_widget.setCurrentWidget(self.supplier)

    def show_purchase_view(self):
        """Affiche la vue de gestion des achats"""
        print("Affichage de la vue de gestion des achats")
        self.central_widget.setCurrentWidget(self.purchasing)
