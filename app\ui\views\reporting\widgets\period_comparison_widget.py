"""
Widget pour comparer les performances entre différentes périodes.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QComboBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox, QDateEdit, QTabWidget
)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QIcon, QColor, QFont
import asyncio
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from app.core.services.reporting_service import ReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class PeriodComparisonWidget(QWidget):
    """Widget pour comparer les performances entre différentes périodes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.service = None
        self.comparison_data = {}
        self.setup_ui()
        self.init_service()
    
    def init_service(self):
        """Initialise le service de reporting"""
        try:
            db = SessionLocal()
            self.service = ReportingService(db)
        except Exception as e:
            print(f"Erreur lors de l'initialisation du service: {e}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Titre
        title_label = QLabel("Comparaison de Périodes")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # Section de sélection des périodes
        period_frame = QFrame()
        period_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        period_layout = QHBoxLayout(period_frame)
        
        # Période 1
        period_layout.addWidget(QLabel("Période 1:"))
        self.period1_start = QDateEdit()
        self.period1_start.setCalendarPopup(True)
        self.period1_start.setDate(QDate.currentDate().addMonths(-2))
        period_layout.addWidget(self.period1_start)
        
        period_layout.addWidget(QLabel("au"))
        self.period1_end = QDateEdit()
        self.period1_end.setCalendarPopup(True)
        self.period1_end.setDate(QDate.currentDate().addMonths(-1))
        period_layout.addWidget(self.period1_end)
        
        period_layout.addWidget(QLabel(" | "))
        
        # Période 2
        period_layout.addWidget(QLabel("Période 2:"))
        self.period2_start = QDateEdit()
        self.period2_start.setCalendarPopup(True)
        self.period2_start.setDate(QDate.currentDate().addMonths(-1))
        period_layout.addWidget(self.period2_start)
        
        period_layout.addWidget(QLabel("au"))
        self.period2_end = QDateEdit()
        self.period2_end.setCalendarPopup(True)
        self.period2_end.setDate(QDate.currentDate())
        period_layout.addWidget(self.period2_end)
        
        # Bouton de comparaison
        self.compare_button = QPushButton("Comparer")
        self.compare_button.setIcon(QIcon("app/ui/resources/icons/compare.svg"))
        self.compare_button.clicked.connect(self.load_comparison_data)
        period_layout.addWidget(self.compare_button)
        
        period_layout.addStretch()
        main_layout.addWidget(period_frame)
        
        # Onglets pour différents types de comparaisons
        self.tab_widget = QTabWidget()
        
        # Onglet Résumé Général
        self.summary_tab = self.create_summary_tab()
        self.tab_widget.addTab(self.summary_tab, "Résumé Général")
        
        # Onglet Réparations
        self.repairs_tab = self.create_repairs_tab()
        self.tab_widget.addTab(self.repairs_tab, "Réparations")
        
        # Onglet Finances
        self.finances_tab = self.create_finances_tab()
        self.tab_widget.addTab(self.finances_tab, "Finances")
        
        main_layout.addWidget(self.tab_widget)
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()
    
    def create_summary_tab(self):
        """Crée l'onglet de résumé général"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Tableau de comparaison générale
        self.summary_table = QTableWidget()
        self.summary_table.setColumnCount(4)
        self.summary_table.setHorizontalHeaderLabels([
            "Métrique", "Période 1", "Période 2", "Évolution (%)"
        ])
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.summary_table.setAlternatingRowColors(True)
        self.summary_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        layout.addWidget(self.summary_table)
        
        return tab
    
    def create_repairs_tab(self):
        """Crée l'onglet de comparaison des réparations"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Graphique de comparaison des réparations
        self.repairs_figure = Figure(figsize=(12, 6))
        self.repairs_canvas = FigureCanvas(self.repairs_figure)
        layout.addWidget(self.repairs_canvas)
        
        # Tableau détaillé des réparations
        self.repairs_table = QTableWidget()
        self.repairs_table.setColumnCount(4)
        self.repairs_table.setHorizontalHeaderLabels([
            "Métrique", "Période 1", "Période 2", "Évolution"
        ])
        self.repairs_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.repairs_table.setAlternatingRowColors(True)
        self.repairs_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        layout.addWidget(self.repairs_table)
        
        return tab
    
    def create_finances_tab(self):
        """Crée l'onglet de comparaison financière"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Graphique de comparaison financière
        self.finances_figure = Figure(figsize=(12, 6))
        self.finances_canvas = FigureCanvas(self.finances_figure)
        layout.addWidget(self.finances_canvas)
        
        # Tableau détaillé des finances
        self.finances_table = QTableWidget()
        self.finances_table.setColumnCount(4)
        self.finances_table.setHorizontalHeaderLabels([
            "Métrique", "Période 1", "Période 2", "Évolution"
        ])
        self.finances_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.finances_table.setAlternatingRowColors(True)
        self.finances_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        layout.addWidget(self.finances_table)
        
        return tab
    
    def load_comparison_data(self):
        """Charge les données de comparaison"""
        QTimer.singleShot(0, self._load_comparison_data_async)
    
    def _load_comparison_data_async(self):
        """Charge les données de comparaison de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_comparison_data())
        finally:
            loop.close()
    
    async def _load_comparison_data(self):
        """Charge les données de comparaison"""
        if not self.service:
            return
        
        self.loading_overlay.show()
        self.compare_button.setEnabled(False)
        
        try:
            # Récupérer les dates
            period1_start = self.period1_start.date().toPython()
            period1_end = self.period1_end.date().toPython()
            period2_start = self.period2_start.date().toPython()
            period2_end = self.period2_end.date().toPython()
            
            # Charger les données pour les deux périodes
            period1_data = await self.service.get_period_summary(period1_start, period1_end)
            period2_data = await self.service.get_period_summary(period2_start, period2_end)
            
            self.comparison_data = {
                'period1': period1_data,
                'period2': period2_data,
                'period1_dates': (period1_start, period1_end),
                'period2_dates': (period2_start, period2_end)
            }
            
            # Mettre à jour les tableaux et graphiques
            self.update_summary_table()
            self.update_repairs_comparison()
            self.update_finances_comparison()
            
        except Exception as e:
            print(f"Erreur lors du chargement des données de comparaison: {e}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors du chargement: {str(e)}")
        
        finally:
            self.loading_overlay.hide()
            self.compare_button.setEnabled(True)
    
    def update_summary_table(self):
        """Met à jour le tableau de résumé"""
        if not self.comparison_data:
            return
        
        period1 = self.comparison_data['period1']
        period2 = self.comparison_data['period2']
        
        # Métriques à comparer
        metrics = [
            ("Nombre de réparations", "total_repairs", ""),
            ("Chiffre d'affaires", "total_revenue", " DA"),
            ("Coût total", "total_cost", " DA"),
            ("Profit", "total_profit", " DA"),
            ("Durée moyenne", "avg_duration", " h"),
            ("Taux de complétion", "completion_rate", "%"),
        ]
        
        self.summary_table.setRowCount(len(metrics))
        
        for i, (label, key, unit) in enumerate(metrics):
            # Nom de la métrique
            self.summary_table.setItem(i, 0, QTableWidgetItem(label))
            
            # Valeur période 1
            value1 = period1.get(key, 0)
            if isinstance(value1, float):
                value1_str = f"{value1:.2f}{unit}"
            else:
                value1_str = f"{value1}{unit}"
            self.summary_table.setItem(i, 1, QTableWidgetItem(value1_str))
            
            # Valeur période 2
            value2 = period2.get(key, 0)
            if isinstance(value2, float):
                value2_str = f"{value2:.2f}{unit}"
            else:
                value2_str = f"{value2}{unit}"
            self.summary_table.setItem(i, 2, QTableWidgetItem(value2_str))
            
            # Évolution
            if value1 > 0:
                evolution = ((value2 - value1) / value1) * 100
                evolution_str = f"{evolution:+.1f}%"
                evolution_item = QTableWidgetItem(evolution_str)
                
                # Colorer selon l'évolution
                if evolution > 0:
                    evolution_item.setBackground(QColor(200, 255, 200))  # Vert
                elif evolution < 0:
                    evolution_item.setBackground(QColor(255, 200, 200))  # Rouge
                
                self.summary_table.setItem(i, 3, evolution_item)
            else:
                self.summary_table.setItem(i, 3, QTableWidgetItem("N/A"))
    
    def update_repairs_comparison(self):
        """Met à jour la comparaison des réparations"""
        # Implémentation simplifiée - peut être étendue
        pass
    
    def update_finances_comparison(self):
        """Met à jour la comparaison financière"""
        # Implémentation simplifiée - peut être étendue
        pass
