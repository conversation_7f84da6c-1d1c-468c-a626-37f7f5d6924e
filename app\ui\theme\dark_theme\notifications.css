/* Styles pour le centre de notifications - Thème Sombre */
#notificationCenterTitle {
    font-size: 18px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 10px;
}

#notificationList {
    background-color: transparent;
    border: none;
}

#notificationItem {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 8px;
}

#notificationItem:hover {
    background-color: #252525;
    border-color: #444444;
}

#notificationItem[status="unread"] {
    border-left: 4px solid #2196F3;
    background-color: #1A1A2E;
}

#notificationItem[status="read"] {
    background-color: #1A1A1A;
    opacity: 0.8;
}

#notificationItem[priority="high"] {
    border-left: 4px solid #FFB74D;
    background-color: #2A1F0A;
}

#notificationItem[priority="critical"] {
    border-left: 4px solid #CF6679;
    background-color: #2A0A0A;
}

#notificationItem[priority="success"] {
    border-left: 4px solid #81C784;
    background-color: #0A2A0A;
}

#notificationTitle {
    font-weight: bold;
    color: #FFFFFF;
    font-size: 14px;
}

#notificationMessage {
    color: #B3B3B3;
    margin-top: 5px;
    line-height: 1.4;
}

#notificationDate {
    color: #666666;
    font-size: 12px;
    margin-top: 8px;
}

#emptyNotifications {
    color: #666666;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Styles pour le bouton de notification */
#notificationButton {
    background-color: transparent;
    border: none;
    border-radius: 4px;
    padding: 8px;
}

#notificationButton:hover {
    background-color: rgba(33, 150, 243, 0.1);
    border-radius: 4px;
}

#notificationButton:pressed {
    background-color: rgba(33, 150, 243, 0.2);
}

/* Badge de notification */
#notificationBadge {
    background-color: #CF6679;
    color: #FFFFFF;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    min-width: 16px;
    text-align: center;
}

/* Styles pour le menu contextuel */
QMenu {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 5px;
    color: #FFFFFF;
}

QMenu::item {
    padding: 8px 20px 8px 20px;
    border-radius: 3px;
    color: #FFFFFF;
}

QMenu::item:selected {
    background-color: #2196F3;
    color: #FFFFFF;
}

QMenu::item:disabled {
    color: #666666;
}

QMenu::separator {
    height: 1px;
    background-color: #333333;
    margin: 5px 0px 5px 0px;
}

QMenu::indicator {
    width: 16px;
    height: 16px;
}

QMenu::indicator:checked {
    background-color: #2196F3;
    border: 1px solid #1976D2;
    border-radius: 2px;
}

/* Styles pour les boutons d'action dans les notifications */
#notificationActionButton {
    background-color: #2196F3;
    color: #FFFFFF;
    border: none;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 12px;
    margin: 2px;
}

#notificationActionButton:hover {
    background-color: #1976D2;
}

#notificationActionButton:pressed {
    background-color: #0D47A1;
}

#notificationDismissButton {
    background-color: transparent;
    color: #666666;
    border: 1px solid #333333;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 12px;
    margin: 2px;
}

#notificationDismissButton:hover {
    background-color: #333333;
    color: #FFFFFF;
}

/* Styles pour les icônes de notification */
#notificationIcon {
    color: #2196F3;
    font-size: 16px;
    margin-right: 8px;
}

#notificationIcon[type="error"] {
    color: #CF6679;
}

#notificationIcon[type="warning"] {
    color: #FFB74D;
}

#notificationIcon[type="success"] {
    color: #81C784;
}

#notificationIcon[type="info"] {
    color: #4FC3F7;
}

/* Animation pour les nouvelles notifications */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

#notificationItem[new="true"] {
    animation: slideIn 0.3s ease-out;
}

/* Styles pour le panneau de notifications */
#notificationPanel {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 10px;
    max-width: 400px;
    min-width: 300px;
}

#notificationPanelHeader {
    background-color: #1E1E1E;
    border-bottom: 1px solid #333333;
    padding: 10px;
    margin: -10px -10px 10px -10px;
    border-radius: 8px 8px 0 0;
}

#notificationPanelTitle {
    font-size: 16px;
    font-weight: bold;
    color: #FFFFFF;
}

#notificationPanelActions {
    margin-top: 10px;
    text-align: right;
}

#markAllReadButton, #clearAllButton {
    background-color: transparent;
    color: #2196F3;
    border: 1px solid #2196F3;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 12px;
    margin-left: 5px;
}

#markAllReadButton:hover, #clearAllButton:hover {
    background-color: #2196F3;
    color: #FFFFFF;
}

/* Scrollbar pour la liste de notifications */
#notificationList QScrollBar:vertical {
    border: none;
    background-color: #1E1E1E;
    width: 8px;
    margin: 0px;
    border-radius: 4px;
}

#notificationList QScrollBar::handle:vertical {
    background-color: #333333;
    border-radius: 4px;
    min-height: 20px;
}

#notificationList QScrollBar::handle:vertical:hover {
    background-color: #444444;
}

#notificationList QScrollBar::add-line:vertical,
#notificationList QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

#notificationList QScrollBar::add-page:vertical,
#notificationList QScrollBar::sub-page:vertical {
    background: none;
}
