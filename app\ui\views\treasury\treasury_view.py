"""
Vue principale du module de trésorerie.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableView, QComboBox, QDateEdit, QLineEdit,
    QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QTextEdit,
    QMessageBox, QDialog, QDialogButtonBox, QFileDialog, QFrame,
    QSplitter, QStackedWidget, QGridLayout, QScrollArea
)
from PyQt6.QtCore import Qt, QDate, QTimer, pyqtSignal, pyqtSlot, QSortFilterProxyModel
from PyQt6.QtGui import QIcon, QFont
import asyncio
from datetime import datetime, timedelta

from app.core.services.treasury_service import TreasuryService
from app.core.models.treasury import CashRegister, CashRegisterType, TransactionCategory, PaymentMethod
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.utils.event_bus import event_bus
from app.utils.treasury_updater import set_treasury_view, clear_treasury_view
from .widgets.cash_register_widget import CashRegisterWidget
from .widgets.transaction_table_model import TransactionTableModel
from .widgets.expense_table_model import ExpenseTableModel
from .dialogs.cash_register_dialog import CashRegisterDialog
from .dialogs.transaction_dialog import TransactionDialog
from .dialogs.expense_dialog import ExpenseDialog
from .dialogs.transfer_dialog import TransferDialog
from .dialogs.reconciliation_dialog import ReconciliationDialog

class TreasuryView(QWidget):
    """Vue principale du module de trésorerie"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = TreasuryService(self.db)

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Connecter au bus d'événements
        self.connect_event_bus()

        # S'enregistrer dans l'updater de trésorerie
        set_treasury_view(self)

        # Charger les données
        self.init_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        # Se déconnecter du bus d'événements
        try:
            event_bus.treasury_updated.disconnect(self.on_treasury_updated)
            event_bus.cash_transaction_added.disconnect(self.on_cash_transaction_added)
        except:
            pass  # Ignorer les erreurs de déconnexion

        # Effacer la référence dans l'updater
        clear_treasury_view()

        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("TreasuryView: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # En-tête
        header = QLabel("Gestion de la Trésorerie")
        header.setObjectName("sectionHeader")
        main_layout.addWidget(header)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Tableau de bord
        self.dashboard_tab = self._create_dashboard_tab()
        self.tab_widget.addTab(self.dashboard_tab, "Tableau de bord")

        # Onglet Caisses
        self.registers_tab = self._create_registers_tab()
        self.tab_widget.addTab(self.registers_tab, "Caisses")

        # Onglet Transactions
        self.transactions_tab = self._create_transactions_tab()
        self.tab_widget.addTab(self.transactions_tab, "Transactions")

        # Onglet Dépenses
        self.expenses_tab = self._create_expenses_tab()
        self.tab_widget.addTab(self.expenses_tab, "Dépenses")

        main_layout.addWidget(self.tab_widget)

    def _create_dashboard_tab(self):
        """Crée l'onglet Tableau de bord"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Résumé des caisses
        registers_group = QGroupBox("Résumé des caisses")
        registers_layout = QVBoxLayout(registers_group)

        # Solde total
        total_layout = QHBoxLayout()
        total_label = QLabel("Solde total:")
        total_layout.addWidget(total_label)

        self.total_balance_label = QLabel("0.00 DA")
        total_font = self.total_balance_label.font()
        total_font.setBold(True)
        total_font.setPointSize(14)
        self.total_balance_label.setFont(total_font)
        total_layout.addWidget(self.total_balance_label, 1, Qt.AlignmentFlag.AlignRight)

        registers_layout.addLayout(total_layout)

        # Totaux du jour
        today_layout = QHBoxLayout()

        # Entrées du jour
        today_in_layout = QVBoxLayout()
        today_in_title = QLabel("Entrées du jour:")
        today_in_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.today_in_label = QLabel("0.00 DA")
        self.today_in_label.setStyleSheet("color: green;")
        today_in_layout.addWidget(today_in_title)
        today_in_layout.addWidget(self.today_in_label)
        today_layout.addLayout(today_in_layout)

        # Sorties du jour
        today_out_layout = QVBoxLayout()
        today_out_title = QLabel("Sorties du jour:")
        today_out_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.today_out_label = QLabel("0.00 DA")
        self.today_out_label.setStyleSheet("color: red;")
        today_out_layout.addWidget(today_out_title)
        today_out_layout.addWidget(self.today_out_label)
        today_layout.addLayout(today_out_layout)

        # Net du jour
        today_net_layout = QVBoxLayout()
        today_net_title = QLabel("Net du jour:")
        today_net_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.today_net_label = QLabel("0.00 DA")
        self.today_net_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        today_net_layout.addWidget(today_net_title)
        today_net_layout.addWidget(self.today_net_label)
        today_layout.addLayout(today_net_layout)

        today_layout.addStretch()
        registers_layout.addLayout(today_layout)

        # Grille des caisses
        self.registers_scroll = QScrollArea()
        self.registers_scroll.setWidgetResizable(True)
        self.registers_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.registers_widget = QWidget()
        self.registers_grid = QGridLayout(self.registers_widget)
        self.registers_grid.setContentsMargins(0, 0, 0, 0)
        self.registers_grid.setSpacing(10)

        self.registers_scroll.setWidget(self.registers_widget)
        registers_layout.addWidget(self.registers_scroll)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.new_register_button = QPushButton("Nouvelle caisse")
        self.new_register_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        buttons_layout.addWidget(self.new_register_button)

        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        buttons_layout.addWidget(self.refresh_button)

        self.transfer_button = QPushButton("Transfert entre caisses")
        self.transfer_button.setIcon(QIcon("app/ui/resources/icons/transfer.svg"))
        buttons_layout.addWidget(self.transfer_button)

        registers_layout.addLayout(buttons_layout)

        layout.addWidget(registers_group)

        # Transactions récentes
        transactions_group = QGroupBox("Transactions récentes")
        transactions_layout = QVBoxLayout(transactions_group)

        self.recent_transactions_table = QTableView()
        self.recent_transactions_model = TransactionTableModel()
        self.recent_transactions_table.setModel(self.recent_transactions_model)

        transactions_layout.addWidget(self.recent_transactions_table)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.new_transaction_button = QPushButton("Nouvelle transaction")
        self.new_transaction_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        buttons_layout.addWidget(self.new_transaction_button)

        self.new_expense_button = QPushButton("Nouvelle dépense")
        self.new_expense_button.setIcon(QIcon("app/ui/resources/icons/expense.svg"))
        buttons_layout.addWidget(self.new_expense_button)

        transactions_layout.addLayout(buttons_layout)

        layout.addWidget(transactions_group)

        return tab

    def _create_registers_tab(self):
        """Crée l'onglet Caisses"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        self.new_register_button2 = QPushButton("Nouvelle caisse")
        self.new_register_button2.setIcon(QIcon("app/ui/assets/icons/add.svg"))
        toolbar_layout.addWidget(self.new_register_button2)

        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # Grille des caisses
        self.registers_scroll2 = QScrollArea()
        self.registers_scroll2.setWidgetResizable(True)
        self.registers_scroll2.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.registers_widget2 = QWidget()
        self.registers_grid2 = QGridLayout(self.registers_widget2)
        self.registers_grid2.setContentsMargins(0, 0, 0, 0)
        self.registers_grid2.setSpacing(10)

        self.registers_scroll2.setWidget(self.registers_widget2)
        layout.addWidget(self.registers_scroll2)

        return tab

    def _create_transactions_tab(self):
        """Crée l'onglet Transactions"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        self.new_transaction_button2 = QPushButton("Nouvelle transaction")
        self.new_transaction_button2.setIcon(QIcon("app/ui/assets/icons/add.svg"))
        toolbar_layout.addWidget(self.new_transaction_button2)

        # Filtres
        toolbar_layout.addStretch()

        filter_label = QLabel("Caisse:")
        toolbar_layout.addWidget(filter_label)

        self.register_filter = QComboBox()
        self.register_filter.addItem("Toutes", None)
        toolbar_layout.addWidget(self.register_filter)

        date_label = QLabel("Date:")
        toolbar_layout.addWidget(date_label)

        self.date_filter = QDateEdit(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        toolbar_layout.addWidget(self.date_filter)

        payment_method_label = QLabel("Méthode de paiement:")
        toolbar_layout.addWidget(payment_method_label)

        self.payment_method_filter = QComboBox()
        self.payment_method_filter.addItem("Toutes", None)
        for method in PaymentMethod:
            self.payment_method_filter.addItem(method.value.capitalize(), method)
        toolbar_layout.addWidget(self.payment_method_filter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Rechercher...")
        toolbar_layout.addWidget(self.search_edit)

        layout.addLayout(toolbar_layout)

        # Tableau des transactions
        self.transactions_table = QTableView()
        self.transactions_model = TransactionTableModel()
        self.transactions_proxy_model = QSortFilterProxyModel()
        self.transactions_proxy_model.setSourceModel(self.transactions_model)
        self.transactions_table.setModel(self.transactions_proxy_model)

        layout.addWidget(self.transactions_table)

        return tab

    def _create_expenses_tab(self):
        """Crée l'onglet Dépenses"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        self.new_expense_button2 = QPushButton("Nouvelle dépense")
        self.new_expense_button2.setIcon(QIcon("app/ui/resources/icons/expense.svg"))
        toolbar_layout.addWidget(self.new_expense_button2)

        # Filtres
        toolbar_layout.addStretch()

        filter_label = QLabel("Caisse:")
        toolbar_layout.addWidget(filter_label)

        self.register_filter2 = QComboBox()
        self.register_filter2.addItem("Toutes", None)
        toolbar_layout.addWidget(self.register_filter2)

        date_label = QLabel("Date:")
        toolbar_layout.addWidget(date_label)

        self.date_filter2 = QDateEdit(QDate.currentDate())
        self.date_filter2.setCalendarPopup(True)
        toolbar_layout.addWidget(self.date_filter2)

        category_label = QLabel("Catégorie:")
        toolbar_layout.addWidget(category_label)

        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes", None)
        toolbar_layout.addWidget(self.category_filter)

        self.search_edit2 = QLineEdit()
        self.search_edit2.setPlaceholderText("Rechercher...")
        toolbar_layout.addWidget(self.search_edit2)

        layout.addLayout(toolbar_layout)

        # Tableau des dépenses
        self.expenses_table = QTableView()
        self.expenses_model = ExpenseTableModel()
        self.expenses_proxy_model = QSortFilterProxyModel()
        self.expenses_proxy_model.setSourceModel(self.expenses_model)
        self.expenses_table.setModel(self.expenses_proxy_model)

        layout.addWidget(self.expenses_table)

        return tab

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Boutons de la barre d'outils
        self.new_register_button.clicked.connect(self.show_new_register_dialog)
        self.refresh_button.clicked.connect(self._load_data_wrapper)
        self.transfer_button.clicked.connect(self.show_transfer_dialog)

        # Filtres
        self.date_filter.dateChanged.connect(self.filter_transactions)
        self.register_filter.currentIndexChanged.connect(self.filter_transactions)
        self.payment_method_filter.currentIndexChanged.connect(self.filter_transactions)
        self.search_edit.textChanged.connect(self.filter_transactions)

        # Tableau des transactions
        self.transactions_table.selectionModel().selectionChanged.connect(self.on_transaction_selection_changed)
        self.transactions_table.doubleClicked.connect(self.show_transaction_details)

        # Tableau des dépenses
        self.expenses_table.selectionModel().selectionChanged.connect(self.on_expense_selection_changed)
        self.expenses_table.doubleClicked.connect(self.show_expense_details)

        # Boutons d'action
        self.new_transaction_button.clicked.connect(lambda: self.show_new_transaction_dialog())
        self.new_expense_button.clicked.connect(lambda: self.show_new_expense_dialog())

    def connect_event_bus(self):
        """Connecte la vue au bus d'événements"""
        try:
            # Connecter aux signaux de mise à jour de la trésorerie
            event_bus.treasury_updated.connect(self.on_treasury_updated)
            event_bus.cash_transaction_added.connect(self.on_cash_transaction_added)
            print("TreasuryView: Connecté au bus d'événements")
        except Exception as e:
            print(f"Erreur lors de la connexion au bus d'événements: {e}")

    def on_treasury_updated(self):
        """Gère la mise à jour de la trésorerie"""
        print("TreasuryView: Signal treasury_updated reçu - Rafraîchissement des données")
        QTimer.singleShot(100, self._load_data_wrapper)  # Délai pour éviter les conflits

    def on_cash_transaction_added(self, cash_register_id):
        """Gère l'ajout d'une transaction de caisse"""
        print(f"TreasuryView: Signal cash_transaction_added reçu pour la caisse {cash_register_id}")
        QTimer.singleShot(100, self._load_data_wrapper)  # Délai pour éviter les conflits

    async def load_data(self):
        """Charge les données de la trésorerie"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau de bord
            dashboard_data = await self.service.get_treasury_dashboard_data()
            
            # Mettre à jour la grille des caisses
            self._update_registers_grid(dashboard_data["cash_registers"])
            
            # Mettre à jour les totaux
            self.total_balance_label.setText(f"{dashboard_data['total_balance']:.2f} DA")
            self.today_in_label.setText(f"{dashboard_data['today_in']:.2f} DA")
            self.today_out_label.setText(f"{dashboard_data['today_out']:.2f} DA")
            self.today_net_label.setText(f"{dashboard_data['today_net']:.2f} DA")
            
            # Mettre à jour le tableau des transactions
            self.recent_transactions_model.setTransactions(dashboard_data["recent_transactions"])
            
            # Mettre à jour la date du filtre
            self.date_filter.setDate(QDate.currentDate())
            
            # Appliquer les filtres
            self.filter_transactions()
            
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Exécuter la coroutine dans la boucle
            loop.run_until_complete(self.load_data())
            
            # Fermer la boucle après utilisation
            loop.close()
            
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    def _update_registers_grid(self, registers):
        """Met à jour la grille des caisses"""
        # Effacer les widgets existants
        for i in reversed(range(self.registers_grid.count())):
            self.registers_grid.itemAt(i).widget().setParent(None)

        for i in reversed(range(self.registers_grid2.count())):
            self.registers_grid2.itemAt(i).widget().setParent(None)

        # Ajouter les widgets de caisse
        row, col = 0, 0
        for register in registers:
            # Créer le widget
            widget = CashRegisterWidget(register)
            widget.reconcile_clicked.connect(self.show_reconciliation_dialog)
            widget.add_transaction_clicked.connect(self.show_new_transaction_dialog)
            widget.view_transactions_clicked.connect(self.show_register_transactions)

            # Ajouter à la grille du tableau de bord
            self.registers_grid.addWidget(widget, row, col)

            # Ajouter à la grille de l'onglet Caisses
            widget2 = CashRegisterWidget(register)
            widget2.reconcile_clicked.connect(self.show_reconciliation_dialog)
            widget2.add_transaction_clicked.connect(self.show_new_transaction_dialog)
            widget2.view_transactions_clicked.connect(self.show_register_transactions)
            self.registers_grid2.addWidget(widget2, row, col)

            # Passer à la colonne suivante
            col += 1
            if col >= 2:  # 2 colonnes maximum
                col = 0
                row += 1

    def _update_filters(self, registers):
        """Met à jour les filtres"""
        # Filtre de caisse pour les transactions
        self.register_filter.clear()
        self.register_filter.addItem("Toutes", None)
        for register in registers:
            self.register_filter.addItem(register.name, register.id)

        # Filtre de caisse pour les dépenses
        self.register_filter2.clear()
        self.register_filter2.addItem("Toutes", None)
        for register in registers:
            self.register_filter2.addItem(register.name, register.id)

    def _load_expense_categories(self):
        """Charge les catégories de dépenses"""
        # Pour l'instant, on utilise des catégories prédéfinies
        self.category_filter.clear()
        self.category_filter.addItem("Toutes", None)

        categories = [
            "Fournitures de bureau",
            "Loyer",
            "Électricité",
            "Eau",
            "Internet",
            "Téléphone",
            "Carburant",
            "Maintenance",
            "Salaires",
            "Impôts",
            "Assurance",
            "Publicité",
            "Frais bancaires",
            "Frais de livraison",
            "Urgence",
            "Autre"
        ]

        for category in categories:
            self.category_filter.addItem(category, category)

    def filter_transactions(self):
        """Filtre les transactions"""
        # TODO: Implémenter le filtrage des transactions
        pass

    def filter_expenses(self):
        """Filtre les dépenses"""
        # TODO: Implémenter le filtrage des dépenses
        pass

    def show_new_register_dialog(self):
        """Affiche la boîte de dialogue pour créer une nouvelle caisse"""
        dialog = CashRegisterDialog(self)
        if dialog.exec():
            self.init_data()

    def show_transfer_dialog(self):
        """Affiche la boîte de dialogue pour effectuer un transfert entre caisses"""
        dialog = TransferDialog(self)
        if dialog.exec():
            self.init_data()

    def show_new_transaction_dialog(self, register_id=None):
        """Affiche la boîte de dialogue pour ajouter une nouvelle transaction"""
        dialog = TransactionDialog(self, register_id)
        if dialog.exec():
            self.init_data()

    def show_new_expense_dialog(self, register_id=None):
        """Affiche la boîte de dialogue pour ajouter une nouvelle dépense"""
        dialog = ExpenseDialog(self, register_id)
        if dialog.exec():
            self.init_data()

    def show_reconciliation_dialog(self, register_id):
        """Affiche la boîte de dialogue pour réconcilier une caisse"""
        dialog = ReconciliationDialog(self, register_id)
        if dialog.exec():
            self.init_data()

    def show_register_transactions(self, register_id):
        """Affiche les transactions d'une caisse"""
        # Sélectionner l'onglet Transactions
        self.tab_widget.setCurrentIndex(2)

        # Sélectionner la caisse dans le filtre
        index = self.register_filter.findData(register_id)
        if index >= 0:
            self.register_filter.setCurrentIndex(index)

        # Appliquer le filtre
        self.filter_transactions()

    def on_transaction_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau des transactions"""
        has_selection = len(selected.indexes()) > 0
        # Activer/désactiver les boutons d'action en fonction de la sélection
        # TODO: Implémenter la logique des boutons d'action

    def on_expense_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau des dépenses"""
        has_selection = len(selected.indexes()) > 0
        # Activer/désactiver les boutons d'action en fonction de la sélection
        # TODO: Implémenter la logique des boutons d'action

    def show_transaction_details(self, index):
        """Affiche les détails d'une transaction"""
        if not index.isValid():
            return

        # Récupérer la transaction sélectionnée
        source_index = self.transactions_proxy_model.mapToSource(index)
        transaction = self.transactions_model.get_transaction(source_index.row())
        if not transaction:
            return

        # Créer et afficher la boîte de dialogue de détails
        dialog = TransactionDialog(self, transaction=transaction)
        dialog.exec()

    def show_expense_details(self, index):
        """Affiche les détails d'une dépense"""
        if not index.isValid():
            return

        # Récupérer la dépense sélectionnée
        source_index = self.expenses_proxy_model.mapToSource(index)
        expense = self.expenses_model.get_expense(source_index.row())
        if not expense:
            return

        # Créer et afficher la boîte de dialogue de détails
        dialog = ExpenseDialog(self, expense=expense)
        dialog.exec()

    def init_data(self):
        """Initialise les données de la vue"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Charger les données
            loop.run_until_complete(self.load_data())
            
            # Charger les catégories de dépenses
            self._load_expense_categories()
            
            # Fermer la boucle après utilisation
            loop.close()
            
        except Exception as e:
            print(f"Erreur lors de l'initialisation des données: {e}")
            import traceback
            traceback.print_exc()
