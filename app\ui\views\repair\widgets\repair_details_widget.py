from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QFormLayout, QScrollArea, QPushButton
)
from PyQt6.QtCore import Qt
import asyncio
from datetime import datetime

class RepairDetailsWidget(QWidget):
    """Widget d'affichage des détails d'une réparation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair = None
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Créer un widget de défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.Shape.NoFrame)

        # Widget de contenu
        content_widget = QWidget()
        self.form_layout = QFormLayout(content_widget)

        # Informations de base
        self.number_label = QLabel("-")
        self.form_layout.addRow("Numéro:", self.number_label)

        self.status_label = QLabel("-")
        self.form_layout.addRow("Statut:", self.status_label)

        self.priority_label = QLabel("-")
        self.form_layout.addRow("Priorité:", self.priority_label)

        self.creation_date_label = QLabel("-")
        self.form_layout.addRow("Date de création:", self.creation_date_label)

        self.scheduled_date_label = QLabel("-")
        self.form_layout.addRow("Date prévue:", self.scheduled_date_label)

        self.completion_date_label = QLabel("-")
        self.form_layout.addRow("Date de fin:", self.completion_date_label)

        # Informations client et équipement
        self.customer_label = QLabel("-")
        self.form_layout.addRow("Client:", self.customer_label)

        self.customer_phone_label = QLabel("-")
        self.form_layout.addRow("Téléphone client:", self.customer_phone_label)

        self.equipment_label = QLabel("-")
        self.form_layout.addRow("Équipement:", self.equipment_label)

        # Problème et description
        self.form_layout.addRow(QLabel("<b>Problème signalé:</b>"))
        self.reported_issue_label = QLabel("-")
        self.reported_issue_label.setWordWrap(True)
        self.form_layout.addRow(self.reported_issue_label)

        self.form_layout.addRow(QLabel("<b>Description:</b>"))
        self.description_label = QLabel("-")
        self.description_label.setWordWrap(True)
        self.form_layout.addRow(self.description_label)

        # Technicien assigné
        self.technician_label = QLabel("-")
        self.form_layout.addRow("Technicien assigné:", self.technician_label)

        # Garantie
        self.warranty_label = QLabel("-")
        self.form_layout.addRow("Sous garantie:", self.warranty_label)

        # Notes
        self.form_layout.addRow(QLabel("<b>Notes:</b>"))
        self.notes_label = QLabel("-")
        self.notes_label.setWordWrap(True)
        self.form_layout.addRow(self.notes_label)

        # Configurer le widget de défilement
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.edit_button = QPushButton("Modifier")
        buttons_layout.addWidget(self.edit_button)

        buttons_layout.addStretch()

        self.print_button = QPushButton("Imprimer")
        buttons_layout.addWidget(self.print_button)

        main_layout.addLayout(buttons_layout)

        # Connecter les signaux
        self.edit_button.clicked.connect(self.edit_repair)
        self.print_button.clicked.connect(self.print_repair)

    def set_repair(self, repair):
        """Définit la réparation à afficher"""
        self.repair = repair
        self.update_ui()

    def refresh_status(self):
        """Rafraîchit uniquement l'affichage du statut"""
        if self.repair:
            self.status_label.setText(self.get_status_display(self.repair.status))

    def update_ui(self):
        """Met à jour l'interface avec les données de la réparation"""
        if not self.repair:
            self.clear()
            return

        # Informations de base
        self.number_label.setText(self.repair.number)
        self.status_label.setText(self.get_status_display(self.repair.status))
        self.priority_label.setText(self.get_priority_display(self.repair.priority))

        # Dates
        self.creation_date_label.setText(self.format_date(getattr(self.repair, 'created_at', None)))
        self.scheduled_date_label.setText(self.format_date(self.repair.scheduled_date) if self.repair.scheduled_date else "-")
        self.completion_date_label.setText(self.format_date(self.repair.completion_date) if self.repair.completion_date else "-")

        # Client et équipement
        asyncio.create_task(self.load_customer_name())
        asyncio.create_task(self.load_equipment_name())

        # Problème et description
        self.reported_issue_label.setText(self.repair.reported_issue)
        self.description_label.setText(self.repair.description)

        # Technicien assigné
        if hasattr(self.repair, 'technician_id') and self.repair.technician_id:
            asyncio.create_task(self.load_technician_name())
        else:
            self.technician_label.setText("Non assigné")

        # Garantie
        self.warranty_label.setText("Oui" if self.repair.warranty else "Non")

        # Notes
        self.notes_label.setText(self.repair.notes if self.repair.notes else "-")

    def clear(self):
        """Efface les données affichées"""
        self.number_label.setText("-")
        self.status_label.setText("-")
        self.priority_label.setText("-")
        self.creation_date_label.setText("-")
        self.scheduled_date_label.setText("-")
        self.completion_date_label.setText("-")
        self.customer_label.setText("-")
        self.customer_phone_label.setText("-")
        self.equipment_label.setText("-")
        self.reported_issue_label.setText("-")
        self.description_label.setText("-")
        self.technician_label.setText("-")
        self.warranty_label.setText("-")
        self.notes_label.setText("-")

    async def load_customer_name(self):
        """Charge le nom et le téléphone du client"""
        if not self.repair:
            return

        try:
            from app.core.services.customer_service import CustomerService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            service = CustomerService(db)

            customer = await service.get(self.repair.customer_id)
            if customer:
                self.customer_label.setText(customer.name)
                self.customer_phone_label.setText(customer.phone)

                # Stocker le téléphone dans l'objet réparation pour faciliter la recherche
                self.repair.customer_phone = customer.phone

        except Exception as e:
            print(f"Erreur lors du chargement du client: {e}")

    async def load_equipment_name(self):
        """Charge le nom de l'équipement"""
        if not self.repair:
            return

        try:
            # Directly use the equipment information from the repair object
            if hasattr(self.repair, 'brand') and hasattr(self.repair, 'model'):
                self.equipment_label.setText(f"{self.repair.brand} {self.repair.model}")
            elif hasattr(self.repair, 'equipment_id') and self.repair.equipment_id:
                from app.core.services.equipment_service import EquipmentService
                from app.utils.database import SessionLocal

                db = SessionLocal()
                service = EquipmentService(db)

                equipment = await service.get(self.repair.equipment_id)
                if equipment:
                    self.equipment_label.setText(f"{equipment.brand} {equipment.model}")

        except Exception as e:
            print(f"Erreur lors du chargement de l'équipement: {e}")

    async def load_technician_name(self):
        """Charge le nom du technicien"""
        if not self.repair:
            return

        try:
            technician_id = None
            if hasattr(self.repair, 'technician_id') and self.repair.technician_id:
                technician_id = self.repair.technician_id

            if technician_id:
                from app.core.services.user_service import UserService
                from app.utils.database import SessionLocal

                db = SessionLocal()
                service = UserService(db)

                user = await service.get(technician_id)
                if user:
                    self.technician_label.setText(user.full_name)

        except Exception as e:
            print(f"Erreur lors du chargement du technicien: {e}")

    def edit_repair(self):
        """Ouvre la boîte de dialogue d'édition de la réparation"""
        if not self.repair:
            return

        # Obtenir la fenêtre parente principale
        from app.ui.views.repair.repair_view import RepairView
        parent = self
        while parent and not isinstance(parent, RepairView):
            parent = parent.parent()

        if parent and isinstance(parent, RepairView):
            # Utiliser directement la boîte de dialogue de réparation
            from app.ui.views.repair.dialogs.repair_dialog import RepairDialog
            dialog = RepairDialog(parent, repair_id=self.repair.id)
            if dialog.exec():
                # Recharger les données après l'édition
                parent._load_data_wrapper()
                # Recharger les détails de la réparation
                parent._load_repair_details_wrapper(self.repair.id)

    def print_repair(self):
        """Imprime l'ordre de réparation"""
        if not self.repair:
            return

        # Obtenir la fenêtre parente principale
        from app.ui.views.repair.repair_view import RepairView
        parent = self
        while parent and not isinstance(parent, RepairView):
            parent = parent.parent()

        if parent and isinstance(parent, RepairView):
            # Appeler la méthode d'impression de la vue parente
            parent.print_repair_order(self.repair.id)

    def get_status_display(self, status):
        """Retourne l'affichage du statut"""
        from app.core.models.repair import RepairStatus

        status_display = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.DIAGNOSED: "Diagnostiqué",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.WAITING_PARTS: "En attente de pièces",
            RepairStatus.COMPLETED: "Terminé",
            RepairStatus.CANCELLED: "Annulé",
            RepairStatus.ON_HOLD: "En pause",
            RepairStatus.INVOICED: "Facturé",
            RepairStatus.PAID: "Payé"
        }
        return status_display.get(status, str(status))

    def get_priority_display(self, priority):
        """Retourne l'affichage de la priorité"""
        from app.core.models.repair import RepairPriority

        priority_display = {
            RepairPriority.CRITICAL: "Critique",
            RepairPriority.HIGH: "Haute",
            RepairPriority.NORMAL: "Normale",
            RepairPriority.LOW: "Basse"
        }
        return priority_display.get(priority, str(priority))

    def format_date(self, date):
        """Formate une date pour l'affichage"""
        if not date:
            return "-"

        if isinstance(date, datetime):
            return date.strftime("%d/%m/%Y %H:%M")

        return str(date)