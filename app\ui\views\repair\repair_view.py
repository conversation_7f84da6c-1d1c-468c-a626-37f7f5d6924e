from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QStackedWidget, QFrame, QTabWidget, QMessageBox,
    QMenu, QDialog, QSplitter, QInputDialog
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QCursor
import asyncio
import os

from .repair_table_model import RepairTableModel
from .dialogs.repair_dialog import RepairDialog
from .dialogs.payment_dialog import PaymentDialog
from .dialogs.invoice_dialog import InvoiceDialog
from .dialogs.used_parts_dialog import UsedPartsDialog
from .widgets.financial_summary_widget import FinancialSummaryWidget
from ...components.custom_widgets import SearchBar, FilterComboBox, LoadingOverlay
from ...components.custom_filter_proxy_model import CustomFilterProxyModel
from app.core.models.repair import RepairStatus, RepairPriority, PaymentStatus
from app.utils.export_utils import export_table_data
from app.utils.config import get_settings

class RepairView(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        # Use QTimer to properly schedule the async load
        QTimer.singleShot(0, self._init_data)

    def _init_data(self):
        """Initialize data loading"""
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # En-tête
        header = QLabel("Gestion des Réparations")
        header.setObjectName("sectionHeader")
        main_layout.addWidget(header)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        # Toolbar layout is already defined above

        # Bouton d'ajout
        self.add_button = QPushButton("Nouvelle Réparation")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setObjectName("primaryButton")
        toolbar_layout.addWidget(self.add_button)

        # Boutons d'action pour la réparation sélectionnée
        self.invoice_button = QPushButton("Facturer")
        self.invoice_button.setIcon(QIcon("app/ui/resources/icons/invoice.svg"))
        self.invoice_button.setEnabled(False)
        toolbar_layout.addWidget(self.invoice_button)

        self.payment_button = QPushButton("Paiement")
        self.payment_button.setIcon(QIcon("app/ui/resources/icons/payment.svg"))
        self.payment_button.setEnabled(False)
        toolbar_layout.addWidget(self.payment_button)

        self.parts_button = QPushButton("Pièces")
        self.parts_button.setIcon(QIcon("app/ui/resources/icons/parts.svg"))
        self.parts_button.setEnabled(False)
        toolbar_layout.addWidget(self.parts_button)

        # Bouton d'export
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        toolbar_layout.addWidget(self.export_button)

        # Espacement
        toolbar_layout.addStretch()

        # Barre de recherche
        self.search_bar = SearchBar("Rechercher une réparation...")
        toolbar_layout.addWidget(self.search_bar)

        main_layout.addLayout(toolbar_layout)

        # Filtres
        filters_layout = QHBoxLayout()

        # Filtre par statut
        self.status_filter = FilterComboBox("Statut")
        filters_layout.addWidget(self.status_filter)

        # Filtre par priorité
        self.priority_filter = FilterComboBox("Priorité")
        filters_layout.addWidget(self.priority_filter)

        # Filtre par technicien
        self.technician_filter = FilterComboBox("Technicien")
        filters_layout.addWidget(self.technician_filter)

        # Filtre par statut de paiement
        self.payment_status_filter = FilterComboBox("Statut de paiement")
        filters_layout.addWidget(self.payment_status_filter)

        # Espacement
        filters_layout.addStretch()

        main_layout.addLayout(filters_layout)

        # Contenu principal avec splitter
        self.splitter = QSplitter(Qt.Orientation.Vertical)

        # Tableau des réparations
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)

        self.table_view = QTableView()
        self.table_view.setObjectName("repairTable")
        self.table_model = RepairTableModel()
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.horizontalHeader().setStretchLastSection(True)
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Style pour mettre en évidence la sélection
        self.table_view.setStyleSheet("""
            QTableView {
                selection-background-color: #2a82da;
                selection-color: white;
                alternate-background-color: #f5f5f5;
            }
            QTableView::item:selected {
                background-color: #2a82da;
                color: white;
                font-weight: bold;
            }
            QTableView::item:hover {
                background-color: #e0e0e0;
            }
        """)

        table_layout.addWidget(self.table_view)

        # Détails de la réparation
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(0, 0, 0, 0)

        # Onglets pour les détails
        self.details_tabs = QTabWidget()

        # Onglet Informations générales
        from .widgets.repair_details_widget import RepairDetailsWidget
        self.repair_details_widget = RepairDetailsWidget()
        self.details_tabs.addTab(self.repair_details_widget, "Informations")

        # Onglet Pièces utilisées
        from .widgets.used_parts_widget import UsedPartsWidget
        self.used_parts_widget = UsedPartsWidget()
        self.details_tabs.addTab(self.used_parts_widget, "Pièces utilisées")

        # Onglet Finances
        self.financial_summary_widget = FinancialSummaryWidget()
        self.details_tabs.addTab(self.financial_summary_widget, "Finances")

        # Onglet Paiements
        from .widgets.payments_widget import PaymentsWidget
        self.payments_widget = PaymentsWidget()
        self.details_tabs.addTab(self.payments_widget, "Paiements")

        # Onglet Statut avancé
        from .widgets.repair_status_widget import RepairStatusWidget
        self.status_widget = RepairStatusWidget()
        self.details_tabs.addTab(self.status_widget, "Statut")

        # Connecter le signal de changement de statut
        self.status_widget.status_changed.connect(self.on_status_changed)

        # Onglet Photos
        from .widgets.repair_photos_widget import RepairPhotosWidget
        self.photos_widget = RepairPhotosWidget()
        self.details_tabs.addTab(self.photos_widget, "Photos")

        # Onglet Notes techniques
        from .widgets.repair_notes_widget import RepairNotesWidget
        self.notes_widget = RepairNotesWidget()
        self.details_tabs.addTab(self.notes_widget, "Notes techniques")

        details_layout.addWidget(self.details_tabs)

        # Ajouter les widgets au splitter
        self.splitter.addWidget(table_widget)
        self.splitter.addWidget(details_widget)

        # Définir les tailles initiales
        self.splitter.setSizes([int(self.height() * 0.6), int(self.height() * 0.4)])

        main_layout.addWidget(self.splitter)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_repair_dialog)
        self.invoice_button.clicked.connect(self.show_invoice_dialog)
        self.payment_button.clicked.connect(self.show_payment_dialog)
        self.parts_button.clicked.connect(self.show_used_parts_dialog)
        self.export_button.clicked.connect(self.export_repairs)

        self.search_bar.textChanged.connect(self.filter_repairs)
        self.status_filter.currentIndexChanged.connect(self.filter_repairs)
        self.priority_filter.currentIndexChanged.connect(self.filter_repairs)
        self.technician_filter.currentIndexChanged.connect(self.filter_repairs)
        self.payment_status_filter.currentIndexChanged.connect(self.filter_repairs)

        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.table_view.doubleClicked.connect(self.show_edit_repair_dialog)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)

    async def load_data(self):
        """Charge les données des réparations"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            await self.table_model.load_data()

            # Charger les filtres
            await self.load_filters()

            # Ajuster les colonnes
            self.table_view.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    async def load_filters(self):
        """Charge les données des filtres"""
        # Statuts
        self.status_filter.clear()
        self.status_filter.addItem("Tous les statuts", None)
        for status in RepairStatus:
            self.status_filter.addItem(self.get_status_display(status), status.value)

        # Priorités
        self.priority_filter.clear()
        self.priority_filter.addItem("Toutes les priorités", None)
        for priority in RepairPriority:
            self.priority_filter.addItem(self.get_priority_display(priority), priority.value)

        # Techniciens
        self.technician_filter.clear()
        self.technician_filter.addItem("Tous les techniciens", None)
        technicians = await self.get_technicians()
        for technician in technicians:
            display_name = f"{technician.first_name} {technician.last_name}" if hasattr(technician, 'first_name') else (
                technician.email if hasattr(technician, 'email') else str(technician.id)
            )
            self.technician_filter.addItem(display_name, technician.id)

        # Statuts de paiement
        self.payment_status_filter.clear()
        self.payment_status_filter.addItem("Tous les statuts", None)
        for status in PaymentStatus:
            self.payment_status_filter.addItem(self.get_payment_status_display(status), status.value)

    def filter_repairs(self):
        """Applique les filtres sur le tableau des réparations"""
        search_text = self.search_bar.text()
        status = self.status_filter.currentData()
        priority = self.priority_filter.currentData()
        technician = self.technician_filter.currentData()
        payment_status = self.payment_status_filter.currentData()

        # Appliquer les filtres au modèle proxy
        self.proxy_model.set_filters({
            'search': search_text,
            'status': status,
            'priority': priority,
            'technician_id': technician,
            'payment_status': payment_status
        })

    def on_selection_changed(self, selected, _):
        """Gère le changement de sélection dans le tableau"""
        indexes = selected.indexes()
        if indexes:
            # Convertir l'index du modèle proxy en index du modèle source
            source_index = self.proxy_model.mapToSource(indexes[0])
            repair_id = self.table_model.get_repair_id(source_index.row())

            # Activer les boutons d'action
            self.invoice_button.setEnabled(True)
            self.payment_button.setEnabled(True)
            self.parts_button.setEnabled(True)

            # Charger les détails de la réparation de manière asynchrone
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.load_repair_details(repair_id))
            except Exception as e:
                print(f"Erreur lors du chargement des détails: {e}")
                import traceback
                traceback.print_exc()
            finally:
                loop.close()
        else:
            # Désactiver les boutons d'action
            self.invoice_button.setEnabled(False)
            self.payment_button.setEnabled(False)
            self.parts_button.setEnabled(False)

            # Effacer les détails
            self.repair_details_widget.clear()
            self.used_parts_widget.clear()
            self.financial_summary_widget.clear()
            self.payments_widget.clear()
            self.status_widget.clear()
            self.photos_widget.clear()
            self.notes_widget.clear()

    def on_status_changed(self, new_status):
        """Gère le changement de statut d'une réparation"""
        try:
            # Obtenir l'ID de la réparation actuellement sélectionnée
            indexes = self.table_view.selectedIndexes()
            if not indexes:
                return

            source_index = self.proxy_model.mapToSource(indexes[0])
            repair_id = self.table_model.get_repair_id(source_index.row())

            # Rafraîchir uniquement la réparation modifiée dans le modèle de table
            self._refresh_repair_in_table(repair_id)

            # Recharger les détails de la réparation pour mettre à jour l'onglet Informations
            self._load_repair_details_wrapper(repair_id)

        except Exception as e:
            print(f"Erreur lors de la mise à jour après changement de statut: {e}")
            import traceback
            traceback.print_exc()

    def _refresh_repair_in_table(self, repair_id):
        """Rafraîchit une réparation spécifique dans le tableau"""
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.table_model.refresh_repair(repair_id))
            except Exception as e:
                print(f"Erreur lors du rafraîchissement de la réparation: {e}")
            finally:
                loop.close()

        # Exécuter dans un thread séparé pour éviter de bloquer l'interface
        import threading
        thread = threading.Thread(target=run_async)
        thread.start()



    async def load_repair_details(self, repair_id):
        """Charge les détails d'une réparation"""
        self.loading_overlay.show()
        try:
            # Charger la réparation
            repair = await self.get_repair(repair_id)

            # Mettre à jour les widgets de détails
            self.repair_details_widget.set_repair(repair)

            # Charger les pièces utilisées
            used_parts = await self.get_used_parts(repair_id)
            self.used_parts_widget.set_parts(used_parts)

            # Charger le résumé financier
            financial_summary = await self.get_financial_summary(repair_id)
            self.financial_summary_widget.set_data(financial_summary)

            # Charger les paiements
            payments = await self.get_payments(repair_id)
            self.payments_widget.set_payments(payments)

            # Charger le statut avancé
            self.status_widget.set_repair_id(repair_id)

            # Charger les photos
            self.photos_widget.set_repair_id(repair_id)

            # Charger les notes techniques
            self.notes_widget.set_repair_id(repair_id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des détails: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def show_context_menu(self, position):
        """Affiche le menu contextuel pour les réparations"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())
        repair_status = self.table_model.get_repair_status(source_index.row())
        payment_status = self.table_model.get_payment_status(source_index.row())

        menu = QMenu(self)

        # Actions communes
        edit_action = QAction("Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_repair_dialog(indexes[0]))
        menu.addAction(edit_action)

        # Actions spécifiques selon le statut
        if repair_status in [RepairStatus.COMPLETED, RepairStatus.DIAGNOSED]:
            invoice_action = QAction("Facturer", self)
            invoice_action.triggered.connect(lambda: self.show_invoice_dialog())
            menu.addAction(invoice_action)

        if repair_status in [RepairStatus.INVOICED, RepairStatus.PAID] or payment_status in [PaymentStatus.PARTIAL, PaymentStatus.PENDING]:
            payment_action = QAction("Enregistrer un paiement", self)
            payment_action.triggered.connect(lambda: self.show_payment_dialog())
            menu.addAction(payment_action)

        if repair_status in [RepairStatus.IN_PROGRESS, RepairStatus.DIAGNOSED]:
            parts_action = QAction("Ajouter des pièces utilisées", self)
            parts_action.triggered.connect(lambda: self.show_used_parts_dialog())
            menu.addAction(parts_action)

        # Séparateur
        menu.addSeparator()

        # Actions d'impression
        print_menu = QMenu("Imprimer", self)

        print_order_action = QAction("Ordre de réparation", self)
        print_order_action.triggered.connect(lambda: self.print_repair_order(repair_id))
        print_menu.addAction(print_order_action)

        # Ajouter l'option pour imprimer le reçu de dépôt
        print_deposit_receipt_action = QAction("Reçu de dépôt", self)
        print_deposit_receipt_action.triggered.connect(lambda: self.print_deposit_receipt(repair_id))
        print_menu.addAction(print_deposit_receipt_action)

        # Ajouter l'option pour imprimer le reçu de réparation
        if repair_status in [RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]:
            print_repair_receipt_action = QAction("Reçu de réparation", self)
            print_repair_receipt_action.triggered.connect(lambda: self.print_repair_receipt(repair_id))
            print_menu.addAction(print_repair_receipt_action)

        if repair_status in [RepairStatus.INVOICED, RepairStatus.PAID]:
            print_invoice_action = QAction("Facture", self)
            print_invoice_action.triggered.connect(lambda: self.print_invoice(repair_id))
            print_menu.addAction(print_invoice_action)

        if payment_status in [PaymentStatus.PARTIAL, PaymentStatus.PAID]:
            print_receipt_action = QAction("Reçu de paiement", self)
            print_receipt_action.triggered.connect(lambda: self.print_receipt(repair_id))
            print_menu.addAction(print_receipt_action)

        menu.addMenu(print_menu)

        # Exécuter le menu
        menu.exec(self.table_view.viewport().mapToGlobal(position))

    def show_add_repair_dialog(self):
        """Affiche la boîte de dialogue d'ajout de réparation"""
        dialog = RepairDialog(self)
        if dialog.exec():
            self._load_data_wrapper()

    def show_edit_repair_dialog(self, index):
        """Affiche la boîte de dialogue d'édition de réparation"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        repair_id = self.table_model.get_repair_id(source_index.row())

        dialog = RepairDialog(self, repair_id=repair_id)
        if dialog.exec():
            self._load_data_wrapper()

    def show_invoice_dialog(self):
        """Affiche la boîte de dialogue de facturation"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())

        dialog = InvoiceDialog(self, repair_id=repair_id)
        if dialog.exec():
            self._load_data_wrapper()
            # Recharger les détails de manière asynchrone
            self._load_repair_details_wrapper(repair_id)

    def show_payment_dialog(self):
        """Affiche la boîte de dialogue d'enregistrement de paiement"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())

        dialog = PaymentDialog(self, repair_id=repair_id)
        if dialog.exec():
            self._load_data_wrapper()
            # Recharger les détails de manière asynchrone
            self._load_repair_details_wrapper(repair_id)

    def show_used_parts_dialog(self):
        """Affiche la boîte de dialogue d'ajout de pièces utilisées"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())

        dialog = UsedPartsDialog(self, repair_id=repair_id)
        if dialog.exec():
            # Recharger les données des réparations
            self._load_data_wrapper()

            # Recharger les détails de manière asynchrone
            self._load_repair_details_wrapper(repair_id)

    def _load_repair_details_wrapper(self, repair_id):
        """Wrapper pour exécuter load_repair_details de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_repair_details(repair_id))
        except Exception as e:
            print(f"Erreur lors du chargement des détails: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def export_repairs(self):
        """Exporte les données des réparations"""
        from PyQt6.QtWidgets import QFileDialog
        import os

        # Demander à l'utilisateur où enregistrer le fichier
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les réparations",
            os.path.expanduser("~/Documents/Réparations.csv"),
            "Fichiers CSV (*.csv)"
        )

        if file_path:
            # Exporter les données
            export_table_data(self.table_view, file_path)

    # Méthodes d'accès aux données (à implémenter avec votre service de réparation)
    async def get_repair(self, repair_id):
        """Récupère les détails d'une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        try:
            service = RepairService(db)
            return await service.get(repair_id)
        finally:
            db.close()

    async def get_used_parts(self, repair_id):
        """Récupère les pièces utilisées pour une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        try:
            service = RepairService(db)
            repair = await service.get(repair_id)
            return repair.used_parts if hasattr(repair, 'used_parts') else []
        finally:
            db.close()

    async def get_financial_summary(self, repair_id):
        """Récupère le résumé financier d'une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal
        from decimal import Decimal

        db = SessionLocal()
        try:
            service = RepairService(db)
            repair = await service.get(repair_id)

            # Récupérer les paiements
            payments = getattr(repair, 'payments', [])

            # Calculer le total des paiements
            total_paid = sum(getattr(payment, 'amount', 0) for payment in payments)

            # Calculer le solde dû
            final_amount = getattr(repair, 'final_amount', 0)
            balance_due = final_amount - total_paid

            # Create a simple financial summary from the repair object
            return {
                'parts_cost': getattr(repair, 'parts_cost', 0),
                'labor_cost': getattr(repair, 'labor_cost', 0),
                'total_cost': getattr(repair, 'total_cost', 0),
                'tax_amount': getattr(repair, 'tax_amount', 0),
                'discount_amount': getattr(repair, 'discount_amount', 0),
                'final_amount': final_amount,
                'total_paid': total_paid,
                'balance_due': balance_due,
                'payment_status': getattr(repair, 'payment_status', 'PENDING'),
                'due_date': getattr(repair, 'due_date', None)
            }
        finally:
            db.close()

    async def get_payments(self, repair_id):
        """Récupère les paiements d'une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        try:
            service = RepairService(db)
            repair = await service.get(repair_id)
            # Return payments if available, otherwise empty list
            return getattr(repair, 'payments', [])
        finally:
            db.close()

    async def get_technicians(self):
        """Récupère la liste des techniciens"""
        from app.core.services.auth_service import UserService
        from app.core.models.config import SessionLocal

        db = SessionLocal()
        try:
            service = UserService(db)
            return await service.get_technicians()
        finally:
            db.close()

    # Méthodes d'impression
    def print_repair_order(self, repair_id):
        """Imprime l'ordre de réparation"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_order(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")
        finally:
            loop.close()

    async def _print_repair_order(self, repair_id):
        """Version asynchrone de l'impression de l'ordre de réparation"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)
                # Check if the service has the generate_repair_order_pdf method
                if hasattr(service, 'generate_repair_order_pdf'):
                    pdf_path = await service.generate_repair_order_pdf(repair_id)

                    # Ouvrir le PDF
                    import os
                    os.startfile(pdf_path)
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")

    def print_invoice(self, repair_id):
        """Imprime la facture"""
        asyncio.create_task(self._print_invoice(repair_id))

    async def _print_invoice(self, repair_id):
        """Version asynchrone de l'impression de la facture"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)
                # Check if the service has the generate_invoice method
                if hasattr(service, 'generate_invoice'):
                    result = await service.generate_invoice(repair_id)

                    # Ouvrir le PDF
                    import os
                    if isinstance(result, dict) and "invoice_pdf" in result:
                        os.startfile(result["invoice_pdf"])
                    else:
                        QMessageBox.warning(self, "Avertissement", "Le format du résultat n'est pas celui attendu.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de facture n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")

    def print_receipt(self, repair_id):
        """Imprime le reçu de paiement"""
        asyncio.create_task(self._print_receipt(repair_id))

    async def _print_receipt(self, repair_id):
        """Version asynchrone de l'impression du reçu"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Check if the service has the necessary methods
                if hasattr(service, 'get') and hasattr(service, 'pdf_generator'):
                    # Obtenir la réparation
                    repair = await service.get(repair_id)

                    # Obtenir les paiements
                    payments = getattr(repair, 'payments', [])
                    if not payments:
                        QMessageBox.warning(self, "Avertissement", "Aucun paiement trouvé pour cette réparation.")
                        return

                    last_payment = payments[-1]

                    # Générer le reçu
                    pdf_path = await service.pdf_generator.generate_receipt(last_payment, repair)

                    # Ouvrir le PDF
                    import os
                    os.startfile(pdf_path)
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")

    def print_deposit_receipt(self, repair_id):
        """Imprime le reçu de dépôt"""
        # Créer un menu contextuel pour choisir le type d'impression
        menu = QMenu(self)

        # Option pour prévisualiser le PDF
        preview_action = QAction("Prévisualiser", self)
        preview_action.triggered.connect(lambda: self._preview_deposit_receipt_pdf(repair_id))
        menu.addAction(preview_action)

        # Séparateur
        menu.addSeparator()

        # Option pour imprimer en PDF
        pdf_action = QAction("Imprimer en PDF", self)
        pdf_action.triggered.connect(lambda: self._print_deposit_receipt_pdf(repair_id))
        menu.addAction(pdf_action)

        # Option pour imprimer sur une imprimante thermique
        thermal_action = QAction("Imprimer sur imprimante thermique", self)
        thermal_action.triggered.connect(lambda: self._print_deposit_receipt_thermal(repair_id))
        menu.addAction(thermal_action)

        # Afficher le menu
        cursor_pos = QCursor.pos()
        menu.exec(cursor_pos)

    def print_repair_receipt(self, repair_id):
        """Imprime le reçu de réparation"""
        # Créer un menu contextuel pour choisir le type d'impression
        menu = QMenu(self)

        # Option pour prévisualiser le PDF
        preview_action = QAction("Prévisualiser", self)
        preview_action.triggered.connect(lambda: self._preview_repair_receipt_pdf(repair_id))
        menu.addAction(preview_action)

        # Séparateur
        menu.addSeparator()

        # Option pour imprimer en PDF
        pdf_action = QAction("Imprimer en PDF", self)
        pdf_action.triggered.connect(lambda: self._print_repair_receipt_pdf(repair_id))
        menu.addAction(pdf_action)

        # Option pour imprimer sur une imprimante thermique
        thermal_action = QAction("Imprimer sur imprimante thermique", self)
        thermal_action.triggered.connect(lambda: self._print_repair_receipt_thermal(repair_id))
        menu.addAction(thermal_action)

        # Afficher le menu
        cursor_pos = QCursor.pos()
        menu.exec(cursor_pos)

    def _print_deposit_receipt_pdf(self, repair_id):
        """Imprime le reçu de dépôt en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_deposit_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")
        finally:
            loop.close()

    def _print_deposit_receipt_thermal(self, repair_id):
        """Imprime le reçu de dépôt sur une imprimante thermique"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_deposit_receipt_thermal_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")
        finally:
            loop.close()

    async def _print_deposit_receipt_pdf_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de dépôt en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_repair_deposit_receipt'):
                    # Générer le reçu de dépôt
                    pdf_path = await service.print_repair_deposit_receipt(repair_id)

                    # Ouvrir le PDF
                    import os
                    if pdf_path:
                        os.startfile(pdf_path)
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de dépôt.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de dépôt n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")

    async def _print_deposit_receipt_thermal_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de dépôt sur une imprimante thermique"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.utils.escpos_printer import ESCPOSPrinter
            from app.ui.views.settings.dialogs.printer_config_dialog import PrinterConfigDialog

            # Vérifier si le module escpos est installé
            try:
                from app.utils.escpos_printer import ESCPOSPrinter
            except ImportError:
                import sys
                import subprocess
                QMessageBox.information(self, "Installation",
                    "Le module python-escpos est nécessaire pour l'impression thermique. Installation en cours...")
                try:
                    # Installer python-escpos
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-escpos>=3.0"])
                    # Réimporter après installation
                    from app.utils.escpos_printer import ESCPOSPrinter
                except Exception as install_error:
                    QMessageBox.critical(self, "Erreur d'installation",
                        f"Impossible d'installer python-escpos. Erreur : {str(install_error)}\n"
                        "Veuillez installer manuellement en exécutant :\n"
                        "pip install python-escpos>=3.0")
                    return

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Récupérer les données de la réparation
                repair_data = await service.get_repair_with_details(repair_id)
                if not repair_data:
                    QMessageBox.warning(self, "Avertissement", f"Aucune donnée trouvée pour la réparation #{repair_id}")
                    return

                # Récupérer la configuration des imprimantes
                printer_config = PrinterConfigDialog.get_printer_config()
                default_printer = PrinterConfigDialog.get_default_printer()

                if default_printer:
                    # Utiliser l'imprimante par défaut
                    printer_name = default_printer["name"]
                    connection_type = default_printer.get("connection_type", "windows")
                    connection_params = default_printer.get("connection_params", {})

                    # Créer l'imprimante ESC/POS
                    printer = ESCPOSPrinter(
                        printer_name=printer_name,
                        connection_type=connection_type,
                        connection_params=connection_params
                    )

                    # Imprimer le reçu
                    success = printer.print_repair_deposit_receipt(repair_data)

                    if success:
                        QMessageBox.information(self, "Impression", f"Le reçu de dépôt a été imprimé avec succès sur {printer_name}.")
                    else:
                        # Si l'impression échoue avec l'imprimante par défaut, demander à l'utilisateur de sélectionner une autre imprimante
                        QMessageBox.warning(
                            self,
                            "Avertissement",
                            f"Impossible d'imprimer le reçu de dépôt sur l'imprimante par défaut ({printer_name}). Veuillez sélectionner une autre imprimante."
                        )
                        self._print_deposit_receipt_thermal_with_selection(repair_data, printer_config)
                else:
                    # Aucune imprimante par défaut, demander à l'utilisateur de sélectionner une imprimante
                    self._print_deposit_receipt_thermal_with_selection(repair_data, printer_config)
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")

    def _print_repair_receipt_pdf(self, repair_id):
        """Imprime le reçu de réparation en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")
        finally:
            loop.close()

    def _print_repair_receipt_thermal(self, repair_id):
        """Imprime le reçu de réparation sur une imprimante thermique"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_receipt_thermal_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")
        finally:
            loop.close()

    def _preview_repair_receipt_pdf(self, repair_id):
        """Prévisualise le reçu de réparation en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._preview_repair_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de réparation: {str(e)}")
        finally:
            loop.close()

    def _preview_deposit_receipt_pdf(self, repair_id):
        """Prévisualise le reçu de dépôt en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._preview_deposit_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de dépôt: {str(e)}")
        finally:
            loop.close()

    async def _preview_repair_receipt_pdf_async(self, repair_id):
        """Version asynchrone de la prévisualisation du reçu de réparation en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.ui.components.pdf_preview_dialog import PDFPreviewDialog

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_completed_repair_receipt'):
                    # Générer le reçu de réparation
                    pdf_path = await service.print_completed_repair_receipt(repair_id)

                    # Afficher la prévisualisation
                    if pdf_path and os.path.exists(pdf_path):
                        dialog = PDFPreviewDialog(pdf_path, self, "Prévisualisation du reçu de réparation")
                        dialog.exec()
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de réparation.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de réparation n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de réparation: {str(e)}")

    async def _preview_deposit_receipt_pdf_async(self, repair_id):
        """Version asynchrone de la prévisualisation du reçu de dépôt en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.ui.components.pdf_preview_dialog import PDFPreviewDialog

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_repair_deposit_receipt'):
                    # Générer le reçu de dépôt
                    pdf_path = await service.print_repair_deposit_receipt(repair_id)

                    # Afficher la prévisualisation
                    if pdf_path and os.path.exists(pdf_path):
                        dialog = PDFPreviewDialog(pdf_path, self, "Prévisualisation du reçu de dépôt")
                        dialog.exec()
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de dépôt.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de dépôt n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de dépôt: {str(e)}")

    async def _print_repair_receipt_pdf_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de réparation en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_completed_repair_receipt'):
                    # Générer le reçu de réparation
                    pdf_path = await service.print_completed_repair_receipt(repair_id)

                    # Ouvrir le PDF
                    import os
                    if pdf_path:
                        os.startfile(pdf_path)
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de réparation.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de réparation n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")

    async def _print_repair_receipt_thermal_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de réparation sur une imprimante thermique"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.utils.escpos_printer import ESCPOSPrinter
            from app.ui.views.settings.dialogs.printer_config_dialog import PrinterConfigDialog

            # Vérifier si le module escpos est installé
            try:
                from app.utils.escpos_printer import ESCPOSPrinter
            except ImportError:
                import sys
                import subprocess
                QMessageBox.information(self, "Installation",
                    "Le module python-escpos est nécessaire pour l'impression thermique. Installation en cours...")
                try:
                    # Installer python-escpos
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-escpos>=3.0"])
                    # Réimporter après installation
                    from app.utils.escpos_printer import ESCPOSPrinter
                except Exception as install_error:
                    QMessageBox.critical(self, "Erreur d'installation",
                        f"Impossible d'installer python-escpos. Erreur : {str(install_error)}\n"
                        "Veuillez installer manuellement en exécutant :\n"
                        "pip install python-escpos>=3.0")
                    return

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Récupérer les données de la réparation
                repair_data = await service.get_repair_with_details(repair_id)
                if not repair_data:
                    QMessageBox.warning(self, "Avertissement", f"Aucune donnée trouvée pour la réparation #{repair_id}")
                    return

                # Récupérer la configuration des imprimantes
                printer_config = PrinterConfigDialog.get_printer_config()
                default_printer = PrinterConfigDialog.get_default_printer()

                if default_printer:
                    # Utiliser l'imprimante par défaut
                    printer_name = default_printer["name"]
                    connection_type = default_printer.get("connection_type", "windows")
                    connection_params = default_printer.get("connection_params", {})

                    # Créer l'imprimante ESC/POS
                    printer = ESCPOSPrinter(
                        printer_name=printer_name,
                        connection_type=connection_type,
                        connection_params=connection_params
                    )

                    # Imprimer le reçu
                    success = printer.print_repair_receipt(repair_data)

                    if success:
                        QMessageBox.information(self, "Impression", f"Le reçu de réparation a été imprimé avec succès sur {printer_name}.")
                    else:
                        # Si l'impression échoue avec l'imprimante par défaut, demander à l'utilisateur de sélectionner une autre imprimante
                        QMessageBox.warning(
                            self,
                            "Avertissement",
                            f"Impossible d'imprimer le reçu de réparation sur l'imprimante par défaut ({printer_name}). Veuillez sélectionner une autre imprimante."
                        )
                        self._print_repair_receipt_thermal_with_selection(repair_data, printer_config)
                else:
                    # Aucune imprimante par défaut, demander à l'utilisateur de sélectionner une imprimante
                    self._print_repair_receipt_thermal_with_selection(repair_data, printer_config)
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")

    def _print_repair_receipt_thermal_with_selection(self, repair_data, printer_config):
        """Imprime un reçu de réparation sur une imprimante thermique sélectionnée par l'utilisateur"""
        try:
            from app.utils.escpos_printer import ESCPOSPrinter

            # Préparer la liste des imprimantes configurées
            configured_printers = list(printer_config.get("printers", {}).keys())

            if not configured_printers:
                # Si aucune imprimante n'est configurée, utiliser les imprimantes système
                printers = self._get_available_printers()
                if not printers:
                    QMessageBox.warning(self, "Avertissement", "Aucune imprimante n'a été trouvée.")
                    return

                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(printer_name=printer_name, connection_type="windows")
            else:
                # Utiliser les imprimantes configurées
                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", configured_printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Récupérer la configuration de l'imprimante
                printer_config_item = printer_config.get("printers", {}).get(printer_name, {})
                connection_type = printer_config_item.get("connection_type", "windows")
                connection_params = printer_config_item.get("connection_params", {})

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(
                    printer_name=printer_name,
                    connection_type=connection_type,
                    connection_params=connection_params
                )

            # Imprimer le reçu
            success = printer.print_repair_receipt(repair_data)

            if success:
                QMessageBox.information(self, "Impression", f"Le reçu de réparation a été imprimé avec succès sur {printer_name}.")
            else:
                QMessageBox.warning(self, "Avertissement", f"Impossible d'imprimer le reçu de réparation sur {printer_name}.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")

    def _print_deposit_receipt_thermal_with_selection(self, repair_data, printer_config):
        """Imprime un reçu de dépôt sur une imprimante thermique sélectionnée par l'utilisateur"""
        try:
            from app.utils.escpos_printer import ESCPOSPrinter

            # Préparer la liste des imprimantes configurées
            configured_printers = list(printer_config.get("printers", {}).keys())

            if not configured_printers:
                # Si aucune imprimante n'est configurée, utiliser les imprimantes système
                printers = self._get_available_printers()
                if not printers:
                    QMessageBox.warning(self, "Avertissement", "Aucune imprimante n'a été trouvée.")
                    return

                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(printer_name=printer_name, connection_type="windows")
            else:
                # Utiliser les imprimantes configurées
                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", configured_printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Récupérer la configuration de l'imprimante
                printer_config_item = printer_config.get("printers", {}).get(printer_name, {})
                connection_type = printer_config_item.get("connection_type", "windows")
                connection_params = printer_config_item.get("connection_params", {})

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(
                    printer_name=printer_name,
                    connection_type=connection_type,
                    connection_params=connection_params
                )

            # Imprimer le reçu
            success = printer.print_repair_deposit_receipt(repair_data)

            if success:
                QMessageBox.information(self, "Impression", f"Le reçu de dépôt a été imprimé avec succès sur {printer_name}.")
            else:
                QMessageBox.warning(self, "Avertissement", f"Impossible d'imprimer le reçu de dépôt sur {printer_name}.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")

    def _get_available_printers(self):
        """Récupère la liste des imprimantes disponibles"""
        try:
            # Vérifier si nous sommes sur Windows
            import platform
            if platform.system() != 'Windows':
                QMessageBox.warning(self, "Avertissement",
                    "L'impression directe n'est disponible que sous Windows.")
                return []

            # Essayer d'importer win32print avec une gestion d'erreur plus détaillée
            try:
                import win32print
                import win32api
            except ImportError as e:
                import sys
                import subprocess
                import os

                # Vérifier si nous sommes dans un environnement virtuel
                venv_path = os.environ.get('VIRTUAL_ENV')
                if venv_path:
                    pip_path = os.path.join(venv_path, 'Scripts', 'pip.exe')
                else:
                    pip_path = sys.executable.replace('python.exe', 'pip.exe')

                QMessageBox.information(self, "Installation",
                    "Le module win32print est nécessaire pour l'impression. Installation en cours...")

                try:
                    # Essayer d'installer avec pip
                    subprocess.check_call([pip_path, "install", "--upgrade", "pywin32>=305"])

                    # Vérifier si l'installation a réussi
                    try:
                        import win32print
                        import win32api
                    except ImportError:
                        raise Exception("L'installation de pywin32 a échoué")

                except Exception as install_error:
                    error_msg = str(install_error)
                    if "Permission denied" in error_msg:
                        QMessageBox.critical(self, "Erreur d'installation",
                            "Permission refusée lors de l'installation de pywin32.\n"
                            "Veuillez exécuter l'application en tant qu'administrateur.")
                    else:
                        QMessageBox.critical(self, "Erreur d'installation",
                            f"Impossible d'installer pywin32. Erreur : {error_msg}\n"
                            "Veuillez installer manuellement pywin32 en exécutant :\n"
                            "pip install pywin32>=305")
                    return []

            # Vérifier la version de Windows
            try:
                version = win32api.GetVersionEx()
                if version[0] < 10:  # Windows 10 ou supérieur requis
                    QMessageBox.warning(self, "Avertissement",
                        "Votre version de Windows pourrait ne pas être compatible avec certaines fonctionnalités d'impression.")
            except Exception as e:
                print(f"Erreur lors de la vérification de la version de Windows : {e}")

            # Récupérer la liste des imprimantes
            printers = []
            try:
                for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS):
                    printers.append(printer[2])
            except Exception as e:
                print(f"Erreur lors de l'énumération des imprimantes : {e}")
                QMessageBox.warning(self, "Avertissement",
                    "Impossible de récupérer la liste des imprimantes. Vérifiez vos permissions.")

            if not printers:
                QMessageBox.warning(self, "Avertissement",
                    "Aucune imprimante n'a été trouvée sur votre système.")

            return printers

        except Exception as e:
            error_msg = str(e)
            if "Access denied" in error_msg:
                QMessageBox.critical(self, "Erreur",
                    "Accès refusé lors de la récupération des imprimantes.\n"
                    "Veuillez exécuter l'application en tant qu'administrateur.")
            else:
                QMessageBox.critical(self, "Erreur",
                    f"Erreur lors de la récupération des imprimantes : {error_msg}\n"
                    "Veuillez vérifier que vous avez les droits d'administrateur nécessaires.")
            return []

    # Méthodes utilitaires
    def get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.DIAGNOSED: "Diagnostiqué",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.WAITING_PARTS: "En attente de pièces",
            RepairStatus.COMPLETED: "Terminé",
            RepairStatus.CANCELLED: "Annulé",
            RepairStatus.ON_HOLD: "En pause",
            RepairStatus.INVOICED: "Facturé",
            RepairStatus.PAID: "Payé"
        }
        return status_display.get(status, str(status))

    def get_priority_display(self, priority):
        """Retourne l'affichage de la priorité"""
        priority_display = {
            RepairPriority.CRITICAL: "Critique",
            RepairPriority.HIGH: "Haute",
            RepairPriority.NORMAL: "Normale",
            RepairPriority.LOW: "Basse"
        }
        return priority_display.get(priority, str(priority))

    def get_payment_status_display(self, status):
        """Retourne l'affichage du statut de paiement"""
        status_display = {
            PaymentStatus.PENDING: "En attente",
            PaymentStatus.PARTIAL: "Partiel",
            PaymentStatus.PAID: "Payé",
            PaymentStatus.OVERDUE: "En retard",
            PaymentStatus.CANCELLED: "Annulé"
        }
        return status_display.get(status, str(status))




