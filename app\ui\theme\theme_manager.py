from PyQt6.QtCore import QObject, pyqtSignal, QSettings
from PyQt6.QtWidgets import QApplication
from pathlib import Path
import json
import os

class ThemeManager(QObject):
    """Gestionnaire de thèmes de l'application"""

    # Signal émis lors du changement de thème
    themeChanged = pyqtSignal(str)

    # Thèmes disponibles
    AVAILABLE_THEMES = ["light", "dark"]
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.theme_dir = Path(__file__).parent
        self.settings = QSettings("GestionApp", "Themes")

        # Charger le thème sauvegardé
        saved_theme = self.settings.value("current_theme", "light")
        if saved_theme in self.AVAILABLE_THEMES:
            self.current_theme = saved_theme
        
    def load_theme(self, theme_name: str) -> dict:
        """Charge un thème depuis les fichiers de configuration"""
        theme_path = self.theme_dir / f"{theme_name}_theme/theme.json"
        
        try:
            with open(theme_path, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
            return theme_data
        except FileNotFoundError:
            raise ValueError(f"Theme '{theme_name}' not found")
            
    def get_stylesheet(self, theme_name: str) -> str:
        """Récupère la feuille de style QSS du thème"""
        theme_path = self.theme_dir / f"{theme_name}_theme/style.qss"
        
        try:
            with open(theme_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            raise ValueError(f"Stylesheet for theme '{theme_name}' not found")
            
    def switch_theme(self, theme_name: str):
        """Change le thème actif"""
        if theme_name not in self.AVAILABLE_THEMES:
            raise ValueError(f"Invalid theme name: {theme_name}")

        self.current_theme = theme_name

        # Sauvegarder le thème
        self.settings.setValue("current_theme", theme_name)

        # Émettre le signal
        self.themeChanged.emit(theme_name)
        
    def get_color(self, color_name: str) -> str:
        """Récupère une couleur du thème actuel"""
        theme_data = self.load_theme(self.current_theme)
        colors = theme_data.get("colors", {})

        # Support pour les couleurs imbriquées (ex: "text.primary")
        if "." in color_name:
            keys = color_name.split(".")
            value = colors
            for key in keys:
                if isinstance(value, dict):
                    value = value.get(key)
                else:
                    return None
            return value

        return colors.get(color_name)

    def get_all_stylesheets(self, theme_name: str) -> str:
        """Récupère toutes les feuilles de style d'un thème"""
        stylesheets = []
        theme_path = self.theme_dir / f"{theme_name}_theme"

        # Fichiers CSS à charger dans l'ordre
        css_files = [
            "style.qss",
            "notifications.css",
            "reporting.css",
            "equipment.css",
            "treeview.css",
            "icons.css"
        ]

        for css_file in css_files:
            file_path = theme_path / css_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        stylesheets.append(f"/* {css_file} */\n{content}\n")
                except Exception as e:
                    print(f"Erreur lors du chargement de {css_file}: {e}")

        return "\n".join(stylesheets)

    def is_dark_theme(self) -> bool:
        """Vérifie si le thème actuel est sombre"""
        return self.current_theme == "dark"

    def toggle_theme(self):
        """Bascule entre thème clair et sombre"""
        new_theme = "dark" if self.current_theme == "light" else "light"
        self.switch_theme(new_theme)

    def get_theme_info(self, theme_name: str = None) -> dict:
        """Récupère les informations d'un thème"""
        if theme_name is None:
            theme_name = self.current_theme

        try:
            return self.load_theme(theme_name)
        except ValueError:
            return {}

    def get_available_themes(self) -> list:
        """Récupère la liste des thèmes disponibles"""
        return self.AVAILABLE_THEMES.copy()

    def apply_theme_to_app(self, app: QApplication = None):
        """Applique le thème actuel à l'application"""
        if app is None:
            app = QApplication.instance()

        if app:
            stylesheet = self.get_all_stylesheets(self.current_theme)
            app.setStyleSheet(stylesheet)

    def get_icon_path(self, icon_name: str, theme_name: str = None) -> str:
        """Récupère le chemin d'une icône pour un thème donné"""
        if theme_name is None:
            theme_name = self.current_theme

        theme_path = self.theme_dir / f"{theme_name}_theme" / "icons"
        icon_path = theme_path / f"{icon_name}.png"

        if icon_path.exists():
            return str(icon_path)

        # Fallback vers les icônes communes
        common_path = self.theme_dir / "common" / "icons" / f"{icon_name}.png"
        if common_path.exists():
            return str(common_path)

        return ""