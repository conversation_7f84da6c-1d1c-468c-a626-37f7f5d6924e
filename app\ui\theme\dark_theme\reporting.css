/* Styles pour la vue de reporting - Thème Sombre */
#reportingHeader {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 15px;
    padding: 10px 0;
}

#reportingTabs {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
}

#reportingTabs::pane {
    border: none;
    background-color: #1E1E1E;
    padding: 15px;
    border-radius: 0 0 4px 4px;
}

#reportingTabs QTabBar::tab {
    background-color: #121212;
    border: 1px solid #333333;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 10px 16px;
    margin-right: 2px;
    color: #B3B3B3;
    min-width: 80px;
}

#reportingTabs QTabBar::tab:selected {
    background-color: #1E1E1E;
    border-bottom: 2px solid #2196F3;
    color: #2196F3;
    font-weight: bold;
}

#reportingTabs QTabBar::tab:hover:not(:selected) {
    background-color: #252525;
    color: #FFFFFF;
    border-color: #444444;
}

#reportingTabs QTabBar::tab:disabled {
    color: #666666;
    background-color: #0F0F0F;
}

/* Styles pour les widgets de KPI */
#kpiWidget {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 20px;
    margin: 8px;
    min-height: 100px;
}

#kpiWidget:hover {
    border-color: #2196F3;
    background-color: #252525;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#kpiTitle {
    font-size: 14px;
    color: #B3B3B3;
    margin-bottom: 8px;
    font-weight: 500;
}

#kpiValue {
    font-size: 28px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 5px;
}

#kpiChange {
    font-size: 12px;
    font-weight: 500;
}

#kpiChange[trend="up"] {
    color: #81C784;
}

#kpiChange[trend="down"] {
    color: #CF6679;
}

#kpiChange[trend="neutral"] {
    color: #B3B3B3;
}

#kpiIcon {
    font-size: 24px;
    color: #2196F3;
    margin-bottom: 10px;
}

/* Styles pour les widgets de graphique */
#chartWidget {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 20px;
    margin: 8px;
}

#chartTitle {
    font-size: 18px;
    font-weight: bold;
    color: #FFFFFF;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333333;
}

#chartSubtitle {
    font-size: 14px;
    color: #B3B3B3;
    margin-bottom: 10px;
}

/* Styles pour les tableaux de rapport */
#reportTable {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    gridline-color: #333333;
    selection-background-color: #2196F3;
    selection-color: #FFFFFF;
}

#reportTable::item {
    padding: 8px;
    border-bottom: 1px solid #333333;
    color: #FFFFFF;
}

#reportTable::item:selected {
    background-color: #2196F3;
    color: #FFFFFF;
}

#reportTable::item:hover {
    background-color: #252525;
}

#reportTable QHeaderView::section {
    background-color: #121212;
    color: #FFFFFF;
    padding: 10px;
    border: 1px solid #333333;
    font-weight: bold;
}

#reportTable QHeaderView::section:hover {
    background-color: #252525;
}

/* Styles pour les filtres de rapport */
#reportFilters {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

#reportFilters QLabel {
    color: #FFFFFF;
    font-weight: 500;
    margin-bottom: 5px;
}

#reportFilters QComboBox {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    min-width: 120px;
}

#reportFilters QComboBox:hover {
    border-color: #2196F3;
}

#reportFilters QComboBox::drop-down {
    border: none;
    width: 20px;
}

#reportFilters QComboBox::down-arrow {
    image: url(:/icons/arrow_down_white.png);
    width: 12px;
    height: 12px;
}

#reportFilters QDateEdit {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    min-width: 120px;
}

#reportFilters QDateEdit:hover {
    border-color: #2196F3;
}

/* Styles pour les boutons de rapport */
#reportButton {
    background-color: #2196F3;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: bold;
    margin: 5px;
}

#reportButton:hover {
    background-color: #1976D2;
}

#reportButton:pressed {
    background-color: #0D47A1;
}

#reportButton:disabled {
    background-color: #333333;
    color: #666666;
}

#exportButton {
    background-color: #81C784;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: bold;
    margin: 5px;
}

#exportButton:hover {
    background-color: #66BB6A;
}

#refreshButton {
    background-color: #FFB74D;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: bold;
    margin: 5px;
}

#refreshButton:hover {
    background-color: #FFA726;
}

/* Styles pour les cartes de statistiques */
#statsCard {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 20px;
    margin: 10px;
    min-width: 200px;
}

#statsCard:hover {
    border-color: #2196F3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

#statsCardTitle {
    font-size: 16px;
    font-weight: bold;
    color: #FFFFFF;
    margin-bottom: 10px;
}

#statsCardValue {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 5px;
}

#statsCardDescription {
    font-size: 12px;
    color: #B3B3B3;
}

/* Styles pour les alertes dans les rapports */
#reportAlert {
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
    font-weight: 500;
}

#reportAlert[type="info"] {
    background-color: rgba(79, 195, 247, 0.1);
    border: 1px solid #4FC3F7;
    color: #4FC3F7;
}

#reportAlert[type="warning"] {
    background-color: rgba(255, 183, 77, 0.1);
    border: 1px solid #FFB74D;
    color: #FFB74D;
}

#reportAlert[type="error"] {
    background-color: rgba(207, 102, 121, 0.1);
    border: 1px solid #CF6679;
    color: #CF6679;
}

#reportAlert[type="success"] {
    background-color: rgba(129, 199, 132, 0.1);
    border: 1px solid #81C784;
    color: #81C784;
}

/* Styles pour les graphiques en secteurs */
#pieChartLegend {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    margin-left: 15px;
}

#pieChartLegend QLabel {
    color: #FFFFFF;
    padding: 2px 0;
}

/* Styles pour les barres de progression */
#progressBar {
    background-color: #333333;
    border: 1px solid #444444;
    border-radius: 4px;
    text-align: center;
    color: #FFFFFF;
}

#progressBar::chunk {
    background-color: #2196F3;
    border-radius: 3px;
}

/* Styles pour les indicateurs de tendance */
#trendIndicator {
    font-size: 14px;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 12px;
    margin: 2px;
}

#trendIndicator[trend="up"] {
    background-color: rgba(129, 199, 132, 0.2);
    color: #81C784;
    border: 1px solid #81C784;
}

#trendIndicator[trend="down"] {
    background-color: rgba(207, 102, 121, 0.2);
    color: #CF6679;
    border: 1px solid #CF6679;
}

#trendIndicator[trend="stable"] {
    background-color: rgba(179, 179, 179, 0.2);
    color: #B3B3B3;
    border: 1px solid #B3B3B3;
}

/* Styles pour les tooltips */
QToolTip {
    background-color: #2C2C2C;
    color: #FFFFFF;
    border: 1px solid #444444;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
}
