"""
Widget pour gérer les statuts avancés des réparations.
"""
import asyncio
from datetime import datetime
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QComboBox, QDialog, QDateTimeEdit, QTextEdit, QMessageBox,
    QListWidget, QListWidgetItem, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QMetaObject, Q_ARG, pyqtSlot
from PyQt6.QtGui import QIcon, QColor

from app.core.models.repair import RepairStatus
from app.core.services.repair_service import RepairService
from app.utils.database import SessionLocal
from ....components.custom_widgets import LoadingOverlay

class StatusHistoryItem:
    """Classe pour représenter un élément de l'historique des statuts"""
    
    def __init__(self, status, changed_at, changed_by=None, notes=None):
        self.status = status
        self.changed_at = changed_at
        self.changed_by = changed_by
        self.notes = notes

class ChangeStatusDialog(QDialog):
    """Boîte de dialogue pour changer le statut d'une réparation"""
    
    def __init__(self, parent=None, current_status=None):
        super().__init__(parent)
        self.current_status = current_status
        
        self.setWindowTitle("Changer le statut")
        self.setMinimumWidth(400)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # Statut actuel
        if self.current_status:
            current_layout = QHBoxLayout()
            current_label = QLabel("Statut actuel:")
            current_layout.addWidget(current_label)
            
            current_value = QLabel(self.get_status_display(self.current_status))
            current_value.setStyleSheet(f"color: {self.get_status_color(self.current_status)};")
            current_layout.addWidget(current_value)
            
            layout.addLayout(current_layout)
        
        # Nouveau statut
        status_layout = QHBoxLayout()
        status_label = QLabel("Nouveau statut:")
        status_layout.addWidget(status_label)
        
        self.status_combo = QComboBox()
        for status in RepairStatus:
            self.status_combo.addItem(self.get_status_display(status), status)
        status_layout.addWidget(self.status_combo)
        
        layout.addLayout(status_layout)
        
        # Date de changement
        date_layout = QHBoxLayout()
        date_label = QLabel("Date:")
        date_layout.addWidget(date_label)
        
        self.date_edit = QDateTimeEdit()
        self.date_edit.setDateTime(datetime.now())
        self.date_edit.setCalendarPopup(True)
        date_layout.addWidget(self.date_edit)
        
        layout.addLayout(date_layout)
        
        # Notes
        notes_label = QLabel("Notes:")
        layout.addWidget(notes_label)
        
        self.notes_edit = QTextEdit()
        layout.addWidget(self.notes_edit)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        save_button = QPushButton("Enregistrer")
        save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(save_button)
        
        layout.addLayout(buttons_layout)
        
    def get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.COMPLETED: "Terminée",
            RepairStatus.CANCELLED: "Annulée",
            RepairStatus.WAITING_FOR_PARTS: "En attente de pièces",
            RepairStatus.WAITING_FOR_CUSTOMER: "En attente du client",
            RepairStatus.READY_FOR_PICKUP: "Prête pour récupération",
            RepairStatus.DELIVERED: "Livrée"
        }
        return status_display.get(status, str(status))
        
    def get_status_color(self, status):
        """Retourne la couleur du statut"""
        status_color = {
            RepairStatus.PENDING: "#FFA500",  # Orange
            RepairStatus.IN_PROGRESS: "#0000FF",  # Bleu
            RepairStatus.COMPLETED: "#008000",  # Vert
            RepairStatus.CANCELLED: "#FF0000",  # Rouge
            RepairStatus.WAITING_FOR_PARTS: "#800080",  # Violet
            RepairStatus.WAITING_FOR_CUSTOMER: "#FF00FF",  # Magenta
            RepairStatus.READY_FOR_PICKUP: "#008080",  # Turquoise
            RepairStatus.DELIVERED: "#000000"  # Noir
        }
        return status_color.get(status, "#000000")
        
    def get_status_data(self):
        """Récupère les données du statut"""
        return {
            "status": self.status_combo.currentData(),
            "changed_at": self.date_edit.dateTime().toPyDateTime(),
            "notes": self.notes_edit.toPlainText()
        }

class RepairStatusWidget(QWidget):
    """Widget pour gérer les statuts avancés des réparations"""
    
    status_changed = pyqtSignal(str)  # Signal émis lorsque le statut est modifié
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair_id = None
        self.current_status = None
        self.status_history = []
        
        # Créer une session de base de données
        self.db = SessionLocal()
        self.service = RepairService(self.db)
        
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()
        
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("RepairStatusWidget: Session de base de données fermée")
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # Statut actuel
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.Shape.StyledPanel)
        status_frame.setFrameShadow(QFrame.Shadow.Raised)
        status_layout = QHBoxLayout(status_frame)
        
        status_label = QLabel("Statut actuel:")
        status_label.setStyleSheet("font-weight: bold;")
        status_layout.addWidget(status_label)
        
        self.current_status_label = QLabel()
        status_layout.addWidget(self.current_status_label)
        
        status_layout.addStretch()
        
        self.change_status_button = QPushButton("Changer le statut")
        self.change_status_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.change_status_button.clicked.connect(self.change_status)
        status_layout.addWidget(self.change_status_button)
        
        layout.addWidget(status_frame)
        
        # Historique des statuts
        history_label = QLabel("Historique des statuts")
        history_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(history_label)
        
        self.history_list = QListWidget()
        layout.addWidget(self.history_list)
        
    def set_repair_id(self, repair_id):
        """Définit l'ID de la réparation et charge les données"""
        self.repair_id = repair_id
        if repair_id:
            asyncio.create_task(self.load_status())
        else:
            self.clear()
            
    def clear(self):
        """Efface les données affichées"""
        self.current_status = None
        self.status_history = []
        self.current_status_label.setText("")
        self.history_list.clear()
        
    async def load_status(self):
        """Charge le statut et l'historique de la réparation"""
        if not self.repair_id:
            return
            
        self.loading_overlay.show()
        
        try:
            # Récupérer la réparation
            repair = await self.service.get_repair(self.repair_id)
            
            if repair:
                self.current_status = repair.status
                self.update_current_status()
                
                # Récupérer l'historique des statuts
                self.status_history = await self.service.get_repair_status_history(self.repair_id)
                self.update_history_list()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement du statut: {str(e)}")
        finally:
            self.loading_overlay.hide()
            
    def update_current_status(self):
        """Met à jour l'affichage du statut actuel"""
        if self.current_status:
            status_text = self.get_status_display(self.current_status)
            status_color = self.get_status_color(self.current_status)
            
            self.current_status_label.setText(status_text)
            self.current_status_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
        else:
            self.current_status_label.setText("")
            self.current_status_label.setStyleSheet("")
            
    def update_history_list(self):
        """Met à jour la liste de l'historique des statuts"""
        self.history_list.clear()
        
        for history_item in self.status_history:
            item = QListWidgetItem()
            
            # Texte de l'élément
            status_text = self.get_status_display(history_item.status)
            date_text = history_item.changed_at.strftime("%d/%m/%Y %H:%M")
            
            item.setText(f"{date_text} - {status_text}")
            
            # Couleur du statut
            item.setForeground(QColor(self.get_status_color(history_item.status)))
            
            # Données de l'élément
            item.setData(Qt.ItemDataRole.UserRole, history_item)
            
            self.history_list.addItem(item)
            
    def get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.COMPLETED: "Terminée",
            RepairStatus.CANCELLED: "Annulée",
            RepairStatus.WAITING_FOR_PARTS: "En attente de pièces",
            RepairStatus.WAITING_FOR_CUSTOMER: "En attente du client",
            RepairStatus.READY_FOR_PICKUP: "Prête pour récupération",
            RepairStatus.DELIVERED: "Livrée"
        }
        return status_display.get(status, str(status))
        
    def get_status_color(self, status):
        """Retourne la couleur du statut"""
        status_color = {
            RepairStatus.PENDING: "#FFA500",  # Orange
            RepairStatus.IN_PROGRESS: "#0000FF",  # Bleu
            RepairStatus.COMPLETED: "#008000",  # Vert
            RepairStatus.CANCELLED: "#FF0000",  # Rouge
            RepairStatus.WAITING_FOR_PARTS: "#800080",  # Violet
            RepairStatus.WAITING_FOR_CUSTOMER: "#FF00FF",  # Magenta
            RepairStatus.READY_FOR_PICKUP: "#008080",  # Turquoise
            RepairStatus.DELIVERED: "#000000"  # Noir
        }
        return status_color.get(status, "#000000")
        
    @pyqtSlot(bool, str)
    def _update_ui_after_status_change(self, success, message):
        """Met à jour l'interface après le changement de statut"""
        if success:
            QMessageBox.information(self, "Information", "Le statut a été changé avec succès.")
        else:
            QMessageBox.warning(self, "Avertissement", message)
        self.loading_overlay.hide()

    @pyqtSlot(object)
    def _update_status_data(self, status_data):
        """Met à jour les données de statut dans l'interface"""
        self.current_status = status_data["status"]
        self.update_current_status()
        self.status_history = status_data["history"]
        self.update_history_list()
        self.status_changed.emit(status_data["status"])

    def change_status(self):
        """Change le statut de la réparation"""
        if not self.repair_id:
            QMessageBox.warning(self, "Avertissement", "Aucune réparation sélectionnée.")
            return
            
        # Ouvrir une boîte de dialogue pour changer le statut
        dialog = ChangeStatusDialog(self, self.current_status)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les données du statut
            status_data = dialog.get_status_data()
            
            # Changer le statut
            self.loading_overlay.show()
            
            # Créer une nouvelle boucle d'événements dans un thread séparé
            def run_async():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self._change_status_async(status_data))
                finally:
                    loop.close()
            
            # Lancer l'opération asynchrone dans un thread séparé
            import threading
            thread = threading.Thread(target=run_async)
            thread.start()
            
    async def _change_status_async(self, status_data):
        """Change le statut de manière asynchrone"""
        try:
            # Changer le statut
            success = await self.service.update_repair_status(
                self.repair_id,
                status_data["status"],
                status_data["notes"],
                status_data["changed_at"]
            )
            
            if success:
                # Récupérer l'historique mis à jour
                history = await self.service.get_repair_status_history(self.repair_id)
                
                # Préparer les données pour la mise à jour de l'interface
                update_data = {
                    "status": status_data["status"],
                    "history": history
                }
                
                # Mettre à jour l'interface dans le thread principal
                QMetaObject.invokeMethod(self, "_update_status_data",
                                       Qt.ConnectionType.QueuedConnection,
                                       Q_ARG(object, update_data))
                
                # Afficher le message de succès dans le thread principal
                QMetaObject.invokeMethod(self, "_update_ui_after_status_change",
                                       Qt.ConnectionType.QueuedConnection,
                                       Q_ARG(bool, True),
                                       Q_ARG(str, "Le statut a été changé avec succès."))
            else:
                # Afficher le message d'erreur dans le thread principal
                QMetaObject.invokeMethod(self, "_update_ui_after_status_change",
                                       Qt.ConnectionType.QueuedConnection,
                                       Q_ARG(bool, False),
                                       Q_ARG(str, "Impossible de changer le statut."))
        except Exception as e:
            # Afficher l'erreur dans le thread principal
            QMetaObject.invokeMethod(self, "_update_ui_after_status_change",
                                   Qt.ConnectionType.QueuedConnection,
                                   Q_ARG(bool, False),
                                   Q_ARG(str, f"Erreur lors du changement de statut: {str(e)}"))
